import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { SectorsService } from './sectors.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { PrivilegeGuard } from '../common/guards/privilege.guard';
import { MobileGuard } from '../common/guards/mobile.guard';
import { Privileges } from '../common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';
import {
  CreateSectorDto,
  CreateSectorResponseDto,
} from './dto/create-sector.dto';
import {
  UpdateSectorDto,
  UpdateSectorResponseDto,
} from './dto/update-sector.dto';
import {
  SectorResponseDto,
  SectorsListResponseDto,
} from './dto/sector-response.dto';

@ApiTags('Sectors')
@Controller('sectors')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
export class SectorsController {
  constructor(private readonly sectorsService: SectorsService) {}

  @Post()
  @Privileges(Privilege.USER_MANAGEMENT)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new sector' })
  @ApiResponse({
    status: 201,
    description: 'Sector created successfully',
    type: CreateSectorResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'District not found' })
  @ApiResponse({ status: 409, description: 'Sector already exists' })
  async create(@Body() createSectorDto: CreateSectorDto) {
    return this.sectorsService.create(createSectorDto);
  }

  @Get()
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get all sectors with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by sector name or code',
    example: 'Gitega',
  })
  @ApiQuery({
    name: 'districtId',
    required: false,
    type: Number,
    description: 'Filter by district ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Sectors retrieved successfully',
    type: SectorsListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('search') search?: string,
    @Query('districtId', new ParseIntPipe({ optional: true })) districtId?: number,
  ) {
    return this.sectorsService.findAll(page, limit, search, districtId);
  }

  @Get(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get a sector by ID' })
  @ApiParam({
    name: 'id',
    description: 'Sector ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Sector retrieved successfully',
    type: SectorResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Sector not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.sectorsService.findOne(id);
  }

  @Patch(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Update a sector' })
  @ApiParam({
    name: 'id',
    description: 'Sector ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Sector updated successfully',
    type: UpdateSectorResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Sector or District not found' })
  @ApiResponse({ status: 409, description: 'Sector already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateSectorDto: UpdateSectorDto) {
    return this.sectorsService.update(id, updateSectorDto);
  }

  @Delete(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Delete a sector' })
  @ApiParam({
    name: 'id',
    description: 'Sector ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Sector deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Sector deleted successfully',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - Sector has cells' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Sector not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.sectorsService.remove(id);
  }
}
