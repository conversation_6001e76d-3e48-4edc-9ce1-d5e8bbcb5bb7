import { Gender, EducationLevel, MainWaterSource, WaterAvailability, WaterAvailabilityFrequency, CleanWaterStorageCapacity, WaterSourceDistance, WaterFetchingTime, UnimprovedWaterReason, PwsNonFunctionalityReason, PwsNonFunctionalityDuration, ToiletFacilityType, ToiletFacilityCompleteness, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';
export declare class HouseholdGeneralInfoDto {
    headOfHouseholdName: string;
    genderOfHead: Gender;
    dateOfBirthOfHead: Date;
    educationLevelOfHead: EducationLevel;
    householdSize: number;
    childrenUnder18: number;
    personsWithDisabilities: number;
}
export declare class HouseholdWaterSupplyDto {
    waterSource: MainWaterSource;
    waterAvailability?: WaterAvailability;
    availableDays?: WaterAvailabilityFrequency;
    averageWaterCost?: number;
    storageCapacity: CleanWaterStorageCapacity;
    distanceToSource?: WaterSourceDistance;
    timeToFetch?: WaterFetchingTime;
    jerryCanPrice?: number;
    unimprovedReason?: UnimprovedWaterReason;
    pwsNonFunctionalityReason?: PwsNonFunctionalityReason;
    pwsNonFunctionalityDuration?: PwsNonFunctionalityDuration;
}
export declare class HouseholdSanitationDto {
    toiletType: ToiletFacilityType;
    toiletCompleteness?: ToiletFacilityCompleteness;
    slabConstructionMaterial?: FacilitySlabConstructionMaterial;
    hasToiletFullInLast2Years?: boolean;
    toiletShared?: boolean;
    excretaManagement?: ExcretaManagement;
}
export declare class HouseholdHygieneDto {
    handwashingFacility: boolean;
    handWashingFacilityType?: HandWashingFacilityType;
    handwashingMaterials?: HandWashingMaterial;
}
export declare class HouseholdSolidWasteManagementDto {
    wasteSeparation: boolean;
    wasteManagement?: WasteManagementAfterSeparation;
    treatmentType?: WasteTreatmentType;
    collectionFrequency?: WasteCollectionFrequency;
}
export declare class HouseholdLiquidWasteManagementDto {
    wasterWaterManagement: WasteWaterManagement;
}
export declare class CreateHouseholdSubmissionDto extends BaseCreateSubmissionDto {
    generalInfo: HouseholdGeneralInfoDto;
    waterSupply: HouseholdWaterSupplyDto;
    sanitation: HouseholdSanitationDto;
    hygiene: HouseholdHygieneDto;
    solidWaste: HouseholdSolidWasteManagementDto;
    liquidWaste: HouseholdLiquidWasteManagementDto;
}
export declare class HouseholdSubmissionResponseDto extends BaseSubmissionResponseDto {
    generalInfo: HouseholdGeneralInfoDto;
    waterSupply: HouseholdWaterSupplyDto;
    sanitation: HouseholdSanitationDto;
    hygiene: HouseholdHygieneDto;
    solidWaste: HouseholdSolidWasteManagementDto;
    liquidWaste: HouseholdLiquidWasteManagementDto;
}
