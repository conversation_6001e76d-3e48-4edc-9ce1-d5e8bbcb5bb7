import { MarketCategory, MarketOpeningDays, WaterAvailability, WaterAvailabilityFrequency, CleanWaterStorageCapacity, MainWaterSource, WaterSourceDistance, ToiletFacilityType, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, MarketHandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';
export declare class MarketGeneralInfoDto {
    marketName: string;
    marketCategory: MarketCategory;
    openingDays: MarketOpeningDays;
}
export declare class MarketWaterSupplyDto {
    connectedToPipeline: boolean;
    waterAvailability: WaterAvailability;
    availableDays?: WaterAvailabilityFrequency;
    storageCapacity: CleanWaterStorageCapacity;
    mainWaterSource?: MainWaterSource;
    distanceToSource: WaterSourceDistance;
}
export declare class MarketSanitationDto {
    toiletType: ToiletFacilityType;
    slabConstructionMaterial: FacilitySlabConstructionMaterial;
    totalToilets: number;
    genderSeparation: boolean;
    femaleToilets: number;
    maleToilets: number;
    girlsRoom: boolean;
    disabilityAccess: boolean;
    staffToilets: boolean;
    hasToiletFullInLast2Years: boolean;
    excretaManagement?: ExcretaManagement;
}
export declare class MarketHygieneDto {
    handwashingFacility: boolean;
    facilityType?: HandWashingFacilityType;
    handwashingMaterials?: MarketHandWashingMaterial;
    handWashingfacilityNearToilet?: boolean;
    toiletHandWashingFacilityType?: HandWashingFacilityType;
    toiletHandWashingMaterials?: HandWashingMaterial;
}
export declare class MarketSolidWasteManagementDto {
    wasteSeparation: boolean;
    wasteManagement?: WasteManagementAfterSeparation;
    treatmentType?: WasteTreatmentType;
    collectionFrequency?: WasteCollectionFrequency;
    collectionCost?: number;
}
export declare class MarketLiquidWasteManagementDto {
    liquidWasteManagement: WasteWaterManagement;
}
export declare class CreateMarketSubmissionDto extends BaseCreateSubmissionDto {
    generalInfo: MarketGeneralInfoDto;
    waterSupply: MarketWaterSupplyDto;
    sanitation: MarketSanitationDto;
    hygiene: MarketHygieneDto;
    solidWaste: MarketSolidWasteManagementDto;
    liquidWaste: MarketLiquidWasteManagementDto;
}
export declare class MarketSubmissionResponseDto extends BaseSubmissionResponseDto {
    generalInfo: MarketGeneralInfoDto;
    waterSupply: MarketWaterSupplyDto;
    sanitation: MarketSanitationDto;
    hygiene: MarketHygieneDto;
    solidWaste: MarketSolidWasteManagementDto;
    liquidWaste: MarketLiquidWasteManagementDto;
}
