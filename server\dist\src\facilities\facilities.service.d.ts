import { PrismaService } from '../prisma/prisma.service';
import { CreateHouseholdDto, UpdateHouseholdDto, HouseholdResponseDto, HouseholdsListResponseDto, CreateHouseholdResponseDto, UpdateHouseholdResponseDto } from './dto/household.dto';
import { CreateSchoolDto, UpdateSchoolDto, SchoolResponseDto, SchoolsListResponseDto, CreateSchoolResponseDto, UpdateSchoolResponseDto } from './dto/school.dto';
import { CreateHealthFacilityDto, UpdateHealthFacilityDto, HealthFacilityResponseDto, HealthFacilitiesListResponseDto, CreateHealthFacilityResponseDto, UpdateHealthFacilityResponseDto } from './dto/health-facility.dto';
import { CreatePublicPlaceDto, UpdatePublicPlaceDto, PublicPlaceResponseDto, PublicPlacesListResponseDto, CreatePublicPlaceResponseDto, UpdatePublicPlaceResponseDto } from './dto/public-place.dto';
import { BaseDeleteResponseDto } from './dto/base-facility.dto';
export declare class FacilitiesService {
    private prisma;
    constructor(prisma: PrismaService);
    generateHouseholdNumber(villageId: number): Promise<string>;
    generateSchoolNumber(villageId: number): Promise<string>;
    generateHealthFacilityNumber(villageId: number): Promise<string>;
    generatePublicPlaceNumber(villageId: number): Promise<string>;
    createHousehold(createHouseholdDto: CreateHouseholdDto): Promise<CreateHouseholdResponseDto>;
    findAllHouseholds(page: number, limit: number, villageId: number, search?: string): Promise<HouseholdsListResponseDto>;
    findOneHousehold(id: string): Promise<HouseholdResponseDto>;
    updateHousehold(id: string, updateHouseholdDto: UpdateHouseholdDto): Promise<UpdateHouseholdResponseDto>;
    removeHousehold(id: string): Promise<BaseDeleteResponseDto>;
    private formatHouseholdResponse;
    createSchool(createSchoolDto: CreateSchoolDto): Promise<CreateSchoolResponseDto>;
    findAllSchools(page: number, limit: number, villageId: number, search?: string): Promise<SchoolsListResponseDto>;
    findOneSchool(id: string): Promise<SchoolResponseDto>;
    updateSchool(id: string, updateSchoolDto: UpdateSchoolDto): Promise<UpdateSchoolResponseDto>;
    removeSchool(id: string): Promise<BaseDeleteResponseDto>;
    private formatSchoolResponse;
    createHealthFacility(createHealthFacilityDto: CreateHealthFacilityDto): Promise<CreateHealthFacilityResponseDto>;
    findAllHealthFacilities(page: number, limit: number, villageId: number, search?: string): Promise<HealthFacilitiesListResponseDto>;
    findOneHealthFacility(id: string): Promise<HealthFacilityResponseDto>;
    updateHealthFacility(id: string, updateHealthFacilityDto: UpdateHealthFacilityDto): Promise<UpdateHealthFacilityResponseDto>;
    removeHealthFacility(id: string): Promise<BaseDeleteResponseDto>;
    private formatHealthFacilityResponse;
    createPublicPlace(createPublicPlaceDto: CreatePublicPlaceDto): Promise<CreatePublicPlaceResponseDto>;
    findAllPublicPlaces(page: number, limit: number, villageId: number, search?: string): Promise<PublicPlacesListResponseDto>;
    findOnePublicPlace(id: string): Promise<PublicPlaceResponseDto>;
    updatePublicPlace(id: string, updatePublicPlaceDto: UpdatePublicPlaceDto): Promise<UpdatePublicPlaceResponseDto>;
    removePublicPlace(id: string): Promise<BaseDeleteResponseDto>;
    private formatPublicPlaceResponse;
    private handleLocationForFacility;
}
