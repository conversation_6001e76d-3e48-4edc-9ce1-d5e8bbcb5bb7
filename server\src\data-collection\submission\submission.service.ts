import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { CreateHouseholdSubmissionDto } from './dto/household.dto';
import { CreateSchoolSubmissionDto } from './dto/school.dto';
import { CreateHealthFacilitySubmissionDto } from './dto/health-facility.dto';
import { CreateMarketSubmissionDto } from './dto/public-place.dto';
import { BaseCreateSubmissionResponseDto } from './dto/base-submission.dto';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class SubmissionService {

    constructor(private prisma: PrismaService) { }

    async createHouseholdSubmission(dto: CreateHouseholdSubmissionDto, userId: string): Promise<BaseCreateSubmissionResponseDto> {
        const household = await this.prisma.houseHold.findUnique({
            where: { id: dto.facilityId },
        });

        if (!household || household.deleted) {
            throw new NotFoundException('Household not found');
        }

        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }
        
        try {

            const result = await this.prisma.$transaction(async (tx) => {

                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        houseHoldId: dto.facilityId,
                        submittedById: userId,
                        HouseHoldGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        HouseHoldWaterSupply: {
                            create: dto.waterSupply,
                        },
                        HouseHoldSanitation: {
                            create: dto.sanitation,
                        },
                        HouseHoldHygiene: {
                            create: dto.hygiene,
                        },
                        HouseHoldSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        HouseHoldLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                })

                return submission;
            })

            return {
                message: 'Household submission created',
                submission: result,
            };

        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException('Failed to create household submission');
        }
    }

    async createSchoolSubmission(dto: CreateSchoolSubmissionDto, userId: string) {

        const school = await this.prisma.school.findUnique({
            where: { id: dto.facilityId },
        });

        if (!school || school.deleted) {
            throw new NotFoundException('School not found');
        }

        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        try {

            const result = await this.prisma.$transaction(async (tx) => {

                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        schoolId: dto.facilityId,
                        submittedById: userId,
                        SchoolGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        SchoolWaterSupply: {
                            create: dto.waterSupply,
                        },
                        SchoolSanitation: {
                            create: dto.sanitation,
                        },
                        SchoolHygiene: {
                            create: dto.hygiene,
                        },
                        SchoolSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        SchoolLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                })

                return submission;
            })

            return {
                message: 'School submission created',
                submission: result,
            };

        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException('Failed to create school submission');
        }
    }

    async createHealthFacilitySubmission(dto: CreateHealthFacilitySubmissionDto, userId: string) {
        const healthFacility = await this.prisma.healthFacility.findUnique({
            where: { id: dto.facilityId },
        });

        if (!healthFacility || healthFacility.deleted) {
            throw new NotFoundException('Health facility not found');
        }

        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        try {

            const result = await this.prisma.$transaction(async (tx) => {

                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        healthFacilityId: dto.facilityId,
                        submittedById: userId,
                        HealthFacilityGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        HealthFacilityWaterSupply: {
                            create: dto.waterSupply,
                        },
                        HealthFacilitySanitation: {
                            create: dto.sanitation,
                        },
                        HealthFacilityHygiene: {
                            create: dto.hygiene,
                        },
                        HealthFacilitySolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        HealthFacilityLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                })

                return submission;
            })

            return {
                message: 'Health facility submission created',
                submission: result,
            };

        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException('Failed to create health facility submission');
        }
    }
}
