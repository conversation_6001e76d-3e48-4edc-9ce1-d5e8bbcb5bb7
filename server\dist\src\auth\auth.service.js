"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../prisma/prisma.service");
const email_service_1 = require("../email/email.service");
const bcrypt = __importStar(require("bcryptjs"));
const speakeasy = __importStar(require("speakeasy"));
const QRCode = __importStar(require("qrcode"));
const uuid_1 = require("uuid");
let AuthService = class AuthService {
    prisma;
    jwtService;
    configService;
    emailService;
    constructor(prisma, jwtService, configService, emailService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
        this.configService = configService;
        this.emailService = emailService;
    }
    async login(loginDto) {
        const { email, password } = loginDto;
        const user = await this.prisma.user.findUnique({
            where: { email },
            include: {
                account: true,
                role: true,
            },
        });
        if (!user || !user.account) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (!user.account.accountVerified) {
            throw new common_1.UnauthorizedException('Account not verified. Please check your email.');
        }
        if (!user.account.password) {
            throw new common_1.UnauthorizedException('Password not set. Please check your email for setup instructions.');
        }
        const isPasswordValid = await bcrypt.compare(password, user.account.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (user.account.is2FAEnabled) {
            const tempToken = (0, uuid_1.v4)();
            const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
            await this.prisma.account.update({
                where: { userId: user.id },
                data: {
                    otpTempToken: tempToken,
                    otpExpiresAt: expiresAt,
                },
            });
            return {
                requires2FA: true,
                tempToken,
                message: 'Please enter your 2FA code',
            };
        }
        return this.generateTokens(user);
    }
    async loginWith2FA(loginWith2FADto) {
        const { email, password, totpCode } = loginWith2FADto;
        const loginResult = await this.login({ email, password });
        if ('requires2FA' in loginResult) {
            return this.verify2FA({
                tempToken: loginResult.tempToken,
                totpCode,
            });
        }
        throw new common_1.BadRequestException('2FA is not enabled for this account');
    }
    async verify2FA(verify2FADto) {
        const { tempToken, totpCode } = verify2FADto;
        console.log(verify2FADto);
        const account = await this.prisma.account.findFirst({
            where: {
                otpTempToken: tempToken,
                otpExpiresAt: {
                    gt: new Date(),
                },
            },
            include: {
                user: {
                    include: {
                        role: true,
                    },
                },
            },
        });
        if (!account) {
            throw new common_1.UnauthorizedException('Invalid or expired temporary token');
        }
        const isValidTotp = speakeasy.totp.verify({
            secret: account.twoFASecret,
            encoding: 'base32',
            token: totpCode,
            window: 2,
        });
        if (!isValidTotp) {
            throw new common_1.UnauthorizedException('Invalid 2FA code');
        }
        await this.prisma.account.update({
            where: { userId: account.userId },
            data: {
                otpTempToken: null,
                otpExpiresAt: null,
            },
        });
        const user = await this.prisma.user.findUnique({
            where: { id: account.userId },
            include: { account: true, role: true },
        });
        return this.generateTokens(user);
    }
    async useRecoveryCode(useRecoveryCodeDto) {
        const { tempToken, recoveryCode } = useRecoveryCodeDto;
        const account = await this.prisma.account.findFirst({
            where: {
                otpTempToken: tempToken,
                otpExpiresAt: {
                    gt: new Date(),
                },
            },
            include: {
                user: {
                    include: {
                        role: true,
                    },
                },
                recoveryCodes: {
                    where: {
                        used: false,
                    },
                },
            },
        });
        if (!account) {
            throw new common_1.UnauthorizedException('Invalid or expired temporary token');
        }
        const validRecoveryCode = account.recoveryCodes.find((code) => code.code === recoveryCode && !code.used);
        if (!validRecoveryCode) {
            throw new common_1.UnauthorizedException('Invalid recovery code');
        }
        await this.prisma.recoveryCode.update({
            where: { id: validRecoveryCode.id },
            data: { used: true },
        });
        await this.prisma.account.update({
            where: { userId: account.userId },
            data: {
                otpTempToken: null,
                otpExpiresAt: null,
            },
        });
        return this.generateTokens(account.user);
    }
    async generateTokens(user) {
        const payload = {
            sub: user.id,
            email: user.email,
            role: user.role.name,
            privileges: user.role.privileges,
        };
        const accessToken = await this.jwtService.signAsync(payload, {
            secret: this.configService.get('JWT_SECRET'),
            expiresIn: this.configService.get('JWT_EXPIRES_IN', '15m'),
        });
        const refreshToken = await this.jwtService.signAsync(payload, {
            secret: this.configService.get('JWT_REFRESH_SECRET'),
            expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d'),
        });
        await this.prisma.account.update({
            where: { userId: user.id },
            data: { refreshToken },
        });
        return {
            accessToken,
            refreshToken,
            user: {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: {
                    name: user.role.name,
                    privileges: user.role.privileges,
                },
            },
            twoFactorEnabled: user.account.is2FAEnabled,
        };
    }
    async refreshToken(refreshTokenDto) {
        const { refreshToken } = refreshTokenDto;
        try {
            const payload = await this.jwtService.verifyAsync(refreshToken, {
                secret: this.configService.get('JWT_REFRESH_SECRET'),
            });
            const account = await this.prisma.account.findFirst({
                where: {
                    userId: payload.sub,
                    refreshToken,
                },
                include: {
                    user: {
                        include: {
                            role: true,
                        },
                    },
                },
            });
            if (!account) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const tokens = await this.generateTokens(account.user);
            return {
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid or expired refresh token');
        }
    }
    async logout(userId) {
        await this.prisma.account.update({
            where: { userId },
            data: {
                refreshToken: null,
                otpTempToken: null,
                otpExpiresAt: null,
            },
        });
        return { message: 'Logged out successfully' };
    }
    async requestPasswordReset(requestPasswordResetDto) {
        const { email } = requestPasswordResetDto;
        const user = await this.prisma.user.findUnique({
            where: { email },
            include: { account: true },
        });
        if (!user || !user.account) {
            return { message: 'If the email exists, a password reset link has been sent.' };
        }
        const resetToken = (0, uuid_1.v4)();
        const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000);
        await this.prisma.account.update({
            where: { userId: user.id },
            data: {
                resetToken,
                resetTokenExpiry,
            },
        });
        await this.emailService.sendPasswordResetEmail(user.email, user.firstName, resetToken);
        return { message: 'If the email exists, a password reset link has been sent.' };
    }
    async resetPassword(resetPasswordDto) {
        const { token, newPassword } = resetPasswordDto;
        const account = await this.prisma.account.findFirst({
            where: {
                resetToken: token,
                resetTokenExpiry: {
                    gt: new Date(),
                },
            },
        });
        if (!account) {
            throw new common_1.BadRequestException('Invalid or expired reset token');
        }
        const hashedPassword = await bcrypt.hash(newPassword, 12);
        await this.prisma.account.update({
            where: { userId: account.userId },
            data: {
                password: hashedPassword,
                resetToken: null,
                resetTokenExpiry: null,
            },
        });
        return { message: 'Password reset successfully' };
    }
    async setInitialPassword(setInitialPasswordDto) {
        const { token, password } = setInitialPasswordDto;
        const account = await this.prisma.account.findFirst({
            where: {
                resetToken: token,
                resetTokenExpiry: {
                    gt: new Date(),
                },
                accountVerified: false,
            },
            include: { user: true },
        });
        if (!account) {
            throw new common_1.BadRequestException('Invalid or expired verification token');
        }
        const hashedPassword = await bcrypt.hash(password, 12);
        await this.prisma.account.update({
            where: { userId: account.userId },
            data: {
                password: hashedPassword,
                accountVerified: true,
                accountVerifiedAt: new Date(),
                resetToken: null,
                resetTokenExpiry: null,
            },
        });
        await this.emailService.send2FASetupEmail(account.user.email, account.user.firstName);
        return { message: 'Password set successfully. Please check your email for 2FA setup instructions.' };
    }
    async setup2FA(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: { account: true },
        });
        if (!user || !user.account) {
            throw new common_1.NotFoundException('User not found');
        }
        if (user.account.is2FAEnabled) {
            throw new common_1.BadRequestException('2FA is already enabled');
        }
        const issuer = this.configService.get('APP_NAME', 'WASH MIS');
        const secret = speakeasy.generateSecret({
            name: `${issuer}: ${user.email}`,
            issuer,
            length: 32,
        });
        await this.prisma.account.update({
            where: { userId },
            data: {
                twoFASecret: secret.base32,
            },
        });
        const qrCodeDataURL = await QRCode.toDataURL(secret.otpauth_url);
        return {
            secret: secret.base32,
            qrCode: qrCodeDataURL,
            manualEntryKey: secret.otpauth_url,
        };
    }
    async enable2FA(userId, setup2FADto) {
        const { totpCode } = setup2FADto;
        const account = await this.prisma.account.findUnique({
            where: { userId },
        });
        if (!account || !account.twoFASecret) {
            throw new common_1.BadRequestException('2FA setup not initiated');
        }
        if (account.is2FAEnabled) {
            throw new common_1.BadRequestException('2FA is already enabled');
        }
        const isValidTotp = speakeasy.totp.verify({
            secret: account.twoFASecret,
            encoding: 'base32',
            token: totpCode,
            window: 2,
        });
        if (!isValidTotp) {
            throw new common_1.BadRequestException('Invalid 2FA code');
        }
        const recoveryCodes = Array.from({ length: 8 }, () => {
            const code = Math.random().toString(36).substring(2, 6).toUpperCase() + '-' +
                Math.random().toString(36).substring(2, 6).toUpperCase() + '-' +
                Math.random().toString(36).substring(2, 6).toUpperCase() + '-' +
                Math.random().toString(36).substring(2, 6).toUpperCase();
            return code;
        });
        await this.prisma.$transaction(async (tx) => {
            await tx.account.update({
                where: { userId },
                data: {
                    is2FAEnabled: true,
                },
            });
            await tx.recoveryCode.createMany({
                data: recoveryCodes.map((code) => ({
                    accountId: userId,
                    code,
                })),
            });
        });
        return {
            message: '2FA has been enabled successfully',
            recoveryCodes,
        };
    }
    async disable2FA(userId, totpCode) {
        const account = await this.prisma.account.findUnique({
            where: { userId },
        });
        if (!account || !account.is2FAEnabled) {
            throw new common_1.BadRequestException('2FA is not enabled');
        }
        const isValidTotp = speakeasy.totp.verify({
            secret: account.twoFASecret,
            encoding: 'base32',
            token: totpCode,
            window: 2,
        });
        if (!isValidTotp) {
            throw new common_1.BadRequestException('Invalid 2FA code');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.account.update({
                where: { userId },
                data: {
                    is2FAEnabled: false,
                    twoFASecret: null,
                },
            });
            await tx.recoveryCode.deleteMany({
                where: { accountId: userId },
            });
        });
        return { message: '2FA has been disabled successfully' };
    }
    async regenerateRecoveryCodes(userId, totpCode) {
        const account = await this.prisma.account.findUnique({
            where: { userId },
        });
        if (!account || !account.is2FAEnabled) {
            throw new common_1.BadRequestException('2FA is not enabled');
        }
        const isValidTotp = speakeasy.totp.verify({
            secret: account.twoFASecret,
            encoding: 'base32',
            token: totpCode,
            window: 2,
        });
        if (!isValidTotp) {
            throw new common_1.BadRequestException('Invalid 2FA code');
        }
        const recoveryCodes = Array.from({ length: 8 }, () => {
            const code = Math.random().toString(36).substring(2, 6).toUpperCase() + '-' +
                Math.random().toString(36).substring(2, 6).toUpperCase() + '-' +
                Math.random().toString(36).substring(2, 6).toUpperCase() + '-' +
                Math.random().toString(36).substring(2, 6).toUpperCase();
            return code;
        });
        await this.prisma.$transaction(async (tx) => {
            await tx.recoveryCode.deleteMany({
                where: { accountId: userId },
            });
            await tx.recoveryCode.createMany({
                data: recoveryCodes.map((code) => ({
                    accountId: userId,
                    code,
                })),
            });
        });
        return { recoveryCodes };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService,
        config_1.ConfigService,
        email_service_1.EmailService])
], AuthService);
//# sourceMappingURL=auth.service.js.map