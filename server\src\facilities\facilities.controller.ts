import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { FacilitiesService } from './facilities.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { PrivilegeGuard } from '../common/guards/privilege.guard';
import { MobileGuard } from '../common/guards/mobile.guard';
import { Privileges } from '../common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';
import { PaginationQueryDto, BaseDeleteResponseDto } from './dto/base-facility.dto';
import {
  CreateHouseholdDto,
  UpdateHouseholdDto,
  HouseholdResponseDto,
  HouseholdsListResponseDto,
  CreateHouseholdResponseDto,
  UpdateHouseholdResponseDto,
} from './dto/household.dto';
import {
  CreateSchoolDto,
  UpdateSchoolDto,
  SchoolResponseDto,
  SchoolsListResponseDto,
  CreateSchoolResponseDto,
  UpdateSchoolResponseDto,
} from './dto/school.dto';
import {
  CreateHealthFacilityDto,
  UpdateHealthFacilityDto,
  HealthFacilityResponseDto,
  HealthFacilitiesListResponseDto,
  CreateHealthFacilityResponseDto,
  UpdateHealthFacilityResponseDto,
} from './dto/health-facility.dto';
import {
  CreatePublicPlaceDto,
  UpdatePublicPlaceDto,
  PublicPlaceResponseDto,
  PublicPlacesListResponseDto,
  CreatePublicPlaceResponseDto,
  UpdatePublicPlaceResponseDto,
} from './dto/public-place.dto';

@ApiTags('Facilities')
@Controller('facilities')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
export class FacilitiesController {
  constructor(private readonly facilitiesService: FacilitiesService) {}

  // ==================== HOUSEHOLD ENDPOINTS ====================

  @Post('households')
  @Privileges(Privilege.DATA_COLLECTION)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new household facility' })
  @ApiResponse({
    status: 201,
    description: 'Household created successfully',
    type: CreateHouseholdResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 409, description: 'Household number already exists' })
  async createHousehold(@Body() createHouseholdDto: CreateHouseholdDto) {
    return this.facilitiesService.createHousehold(createHouseholdDto);
  }

  @Get('households')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get all household facilities with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for filtering households',
    example: '1001',
  })
  @ApiResponse({
    status: 200,
    description: 'Households retrieved successfully',
    type: HouseholdsListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAllHouseholds(@Query() query: PaginationQueryDto) {
    return this.facilitiesService.findAllHouseholds(
      query.page,
      query.limit,
      Number(query.villageId),
      query.search,
    );
  }

  @Get('households/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get household facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Household ID',
    example: 'household_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Household retrieved successfully',
    type: HouseholdResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Household not found' })
  async findOneHousehold(@Param('id') id: string) {
    return this.facilitiesService.findOneHousehold(id);
  }

  @Put('households/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Update household facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Household ID',
    example: 'household_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Household updated successfully',
    type: UpdateHouseholdResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Household not found' })
  @ApiResponse({ status: 409, description: 'Household number already exists' })
  async updateHousehold(
    @Param('id') id: string,
    @Body() updateHouseholdDto: UpdateHouseholdDto,
  ) {
    return this.facilitiesService.updateHousehold(id, updateHouseholdDto);
  }

  @Delete('households/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Soft delete household facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Household ID',
    example: 'household_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Household deleted successfully',
    type: BaseDeleteResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Household not found' })
  async removeHousehold(@Param('id') id: string) {
    return this.facilitiesService.removeHousehold(id);
  }

  // ==================== SCHOOL ENDPOINTS ====================

  @Post('schools')
  @Privileges(Privilege.DATA_COLLECTION)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new school facility' })
  @ApiResponse({
    status: 201,
    description: 'School created successfully',
    type: CreateSchoolResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 409, description: 'School number already exists' })
  async createSchool(@Body() createSchoolDto: CreateSchoolDto) {
    return this.facilitiesService.createSchool(createSchoolDto);
  }

  @Get('schools')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get all school facilities with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for filtering schools',
    example: 'Primary School',
  })
  @ApiResponse({
    status: 200,
    description: 'Schools retrieved successfully',
    type: SchoolsListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAllSchools(@Query() query: PaginationQueryDto) {
    return this.facilitiesService.findAllSchools(
      query.page,
      query.limit,
      Number(query.villageId),
      query.search,
    );
  }

  @Get('schools/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get school facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'School ID',
    example: 'school_123',
  })
  @ApiResponse({
    status: 200,
    description: 'School retrieved successfully',
    type: SchoolResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'School not found' })
  async findOneSchool(@Param('id') id: string) {
    return this.facilitiesService.findOneSchool(id);
  }

  @Put('schools/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Update school facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'School ID',
    example: 'school_123',
  })
  @ApiResponse({
    status: 200,
    description: 'School updated successfully',
    type: UpdateSchoolResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'School not found' })
  @ApiResponse({ status: 409, description: 'School number already exists' })
  async updateSchool(
    @Param('id') id: string,
    @Body() updateSchoolDto: UpdateSchoolDto,
  ) {
    return this.facilitiesService.updateSchool(id, updateSchoolDto);
  }

  @Delete('schools/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Soft delete school facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'School ID',
    example: 'school_123',
  })
  @ApiResponse({
    status: 200,
    description: 'School deleted successfully',
    type: BaseDeleteResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'School not found' })
  async removeSchool(@Param('id') id: string) {
    return this.facilitiesService.removeSchool(id);
  }

  // ==================== HEALTH FACILITY ENDPOINTS ====================

  @Post('health-facilities')
  @Privileges(Privilege.DATA_COLLECTION)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new health facility' })
  @ApiResponse({
    status: 201,
    description: 'Health facility created successfully',
    type: CreateHealthFacilityResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 409, description: 'Health facility number already exists' })
  async createHealthFacility(@Body() createHealthFacilityDto: CreateHealthFacilityDto) {
    return this.facilitiesService.createHealthFacility(createHealthFacilityDto);
  }

  @Get('health-facilities')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get all health facilities with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for filtering health facilities',
    example: 'Health Center',
  })
  @ApiResponse({
    status: 200,
    description: 'Health facilities retrieved successfully',
    type: HealthFacilitiesListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAllHealthFacilities(@Query() query: PaginationQueryDto) {
    return this.facilitiesService.findAllHealthFacilities(
      query.page,
      query.limit,
      Number(query.villageId),
      query.search,
    );
  }

  @Get('health-facilities/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get health facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Health facility ID',
    example: 'health_facility_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Health facility retrieved successfully',
    type: HealthFacilityResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Health facility not found' })
  async findOneHealthFacility(@Param('id') id: string) {
    return this.facilitiesService.findOneHealthFacility(id);
  }

  @Put('health-facilities/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Update health facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Health facility ID',
    example: 'health_facility_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Health facility updated successfully',
    type: UpdateHealthFacilityResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Health facility not found' })
  @ApiResponse({ status: 409, description: 'Health facility number already exists' })
  async updateHealthFacility(
    @Param('id') id: string,
    @Body() updateHealthFacilityDto: UpdateHealthFacilityDto,
  ) {
    return this.facilitiesService.updateHealthFacility(id, updateHealthFacilityDto);
  }

  @Delete('health-facilities/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Soft delete health facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Health facility ID',
    example: 'health_facility_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Health facility deleted successfully',
    type: BaseDeleteResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Health facility not found' })
  async removeHealthFacility(@Param('id') id: string) {
    return this.facilitiesService.removeHealthFacility(id);
  }

  // ==================== PUBLIC PLACE ENDPOINTS ====================

  @Post('public-places')
  @Privileges(Privilege.DATA_COLLECTION)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new public place facility' })
  @ApiResponse({
    status: 201,
    description: 'Public place created successfully',
    type: CreatePublicPlaceResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 409, description: 'Public place number already exists' })
  async createPublicPlace(@Body() createPublicPlaceDto: CreatePublicPlaceDto) {
    return this.facilitiesService.createPublicPlace(createPublicPlaceDto);
  }

  @Get('public-places')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get all public place facilities with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for filtering public places',
    example: 'Market',
  })
  @ApiResponse({
    status: 200,
    description: 'Public places retrieved successfully',
    type: PublicPlacesListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAllPublicPlaces(@Query() query: PaginationQueryDto) {
    return this.facilitiesService.findAllPublicPlaces(
      query.page,
      query.limit,
      Number(query.villageId),
      query.search,
    );
  }

  @Get('public-places/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get public place facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Public place ID',
    example: 'public_place_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Public place retrieved successfully',
    type: PublicPlaceResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Public place not found' })
  async findOnePublicPlace(@Param('id') id: string) {
    return this.facilitiesService.findOnePublicPlace(id);
  }

  @Put('public-places/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Update public place facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Public place ID',
    example: 'public_place_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Public place updated successfully',
    type: UpdatePublicPlaceResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Public place not found' })
  @ApiResponse({ status: 409, description: 'Public place number already exists' })
  async updatePublicPlace(
    @Param('id') id: string,
    @Body() updatePublicPlaceDto: UpdatePublicPlaceDto,
  ) {
    return this.facilitiesService.updatePublicPlace(id, updatePublicPlaceDto);
  }

  @Delete('public-places/:id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Soft delete public place facility by ID' })
  @ApiParam({
    name: 'id',
    description: 'Public place ID',
    example: 'public_place_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Public place deleted successfully',
    type: BaseDeleteResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Public place not found' })
  async removePublicPlace(@Param('id') id: string) {
    return this.facilitiesService.removePublicPlace(id);
  }
}
