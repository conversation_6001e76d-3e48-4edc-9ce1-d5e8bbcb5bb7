"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacilitiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const facilities_service_1 = require("./facilities.service");
const auth_guard_1 = require("../common/guards/auth.guard");
const privilege_guard_1 = require("../common/guards/privilege.guard");
const mobile_guard_1 = require("../common/guards/mobile.guard");
const privileges_decorator_1 = require("../common/decorators/privileges.decorator");
const client_1 = require("@prisma/client");
const base_facility_dto_1 = require("./dto/base-facility.dto");
const household_dto_1 = require("./dto/household.dto");
const school_dto_1 = require("./dto/school.dto");
const health_facility_dto_1 = require("./dto/health-facility.dto");
const public_place_dto_1 = require("./dto/public-place.dto");
let FacilitiesController = class FacilitiesController {
    facilitiesService;
    constructor(facilitiesService) {
        this.facilitiesService = facilitiesService;
    }
    async createHousehold(createHouseholdDto) {
        return this.facilitiesService.createHousehold(createHouseholdDto);
    }
    async findAllHouseholds(query) {
        return this.facilitiesService.findAllHouseholds(query.page, query.limit, Number(query.villageId), query.search);
    }
    async findOneHousehold(id) {
        return this.facilitiesService.findOneHousehold(id);
    }
    async updateHousehold(id, updateHouseholdDto) {
        return this.facilitiesService.updateHousehold(id, updateHouseholdDto);
    }
    async removeHousehold(id) {
        return this.facilitiesService.removeHousehold(id);
    }
    async createSchool(createSchoolDto) {
        return this.facilitiesService.createSchool(createSchoolDto);
    }
    async findAllSchools(query) {
        return this.facilitiesService.findAllSchools(query.page, query.limit, Number(query.villageId), query.search);
    }
    async findOneSchool(id) {
        return this.facilitiesService.findOneSchool(id);
    }
    async updateSchool(id, updateSchoolDto) {
        return this.facilitiesService.updateSchool(id, updateSchoolDto);
    }
    async removeSchool(id) {
        return this.facilitiesService.removeSchool(id);
    }
    async createHealthFacility(createHealthFacilityDto) {
        return this.facilitiesService.createHealthFacility(createHealthFacilityDto);
    }
    async findAllHealthFacilities(query) {
        return this.facilitiesService.findAllHealthFacilities(query.page, query.limit, Number(query.villageId), query.search);
    }
    async findOneHealthFacility(id) {
        return this.facilitiesService.findOneHealthFacility(id);
    }
    async updateHealthFacility(id, updateHealthFacilityDto) {
        return this.facilitiesService.updateHealthFacility(id, updateHealthFacilityDto);
    }
    async removeHealthFacility(id) {
        return this.facilitiesService.removeHealthFacility(id);
    }
    async createPublicPlace(createPublicPlaceDto) {
        return this.facilitiesService.createPublicPlace(createPublicPlaceDto);
    }
    async findAllPublicPlaces(query) {
        return this.facilitiesService.findAllPublicPlaces(query.page, query.limit, Number(query.villageId), query.search);
    }
    async findOnePublicPlace(id) {
        return this.facilitiesService.findOnePublicPlace(id);
    }
    async updatePublicPlace(id, updatePublicPlaceDto) {
        return this.facilitiesService.updatePublicPlace(id, updatePublicPlaceDto);
    }
    async removePublicPlace(id) {
        return this.facilitiesService.removePublicPlace(id);
    }
};
exports.FacilitiesController = FacilitiesController;
__decorate([
    (0, common_1.Post)('households'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new household facility' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Household created successfully',
        type: household_dto_1.CreateHouseholdResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Household number already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [household_dto_1.CreateHouseholdDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "createHousehold", null);
__decorate([
    (0, common_1.Get)('households'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Get all household facilities with pagination and filtering' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search term for filtering households',
        example: '1001',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Households retrieved successfully',
        type: household_dto_1.HouseholdsListResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [base_facility_dto_1.PaginationQueryDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "findAllHouseholds", null);
__decorate([
    (0, common_1.Get)('households/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Get household facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Household ID',
        example: 'household_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Household retrieved successfully',
        type: household_dto_1.HouseholdResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Household not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "findOneHousehold", null);
__decorate([
    (0, common_1.Put)('households/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Update household facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Household ID',
        example: 'household_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Household updated successfully',
        type: household_dto_1.UpdateHouseholdResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Household not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Household number already exists' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, household_dto_1.UpdateHouseholdDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "updateHousehold", null);
__decorate([
    (0, common_1.Delete)('households/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete household facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Household ID',
        example: 'household_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Household deleted successfully',
        type: base_facility_dto_1.BaseDeleteResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Household not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "removeHousehold", null);
__decorate([
    (0, common_1.Post)('schools'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new school facility' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'School created successfully',
        type: school_dto_1.CreateSchoolResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'School number already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [school_dto_1.CreateSchoolDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "createSchool", null);
__decorate([
    (0, common_1.Get)('schools'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Get all school facilities with pagination and filtering' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search term for filtering schools',
        example: 'Primary School',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Schools retrieved successfully',
        type: school_dto_1.SchoolsListResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [base_facility_dto_1.PaginationQueryDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "findAllSchools", null);
__decorate([
    (0, common_1.Get)('schools/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Get school facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'School ID',
        example: 'school_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'School retrieved successfully',
        type: school_dto_1.SchoolResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'School not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "findOneSchool", null);
__decorate([
    (0, common_1.Put)('schools/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Update school facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'School ID',
        example: 'school_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'School updated successfully',
        type: school_dto_1.UpdateSchoolResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'School not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'School number already exists' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, school_dto_1.UpdateSchoolDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "updateSchool", null);
__decorate([
    (0, common_1.Delete)('schools/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete school facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'School ID',
        example: 'school_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'School deleted successfully',
        type: base_facility_dto_1.BaseDeleteResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'School not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "removeSchool", null);
__decorate([
    (0, common_1.Post)('health-facilities'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new health facility' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Health facility created successfully',
        type: health_facility_dto_1.CreateHealthFacilityResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Health facility number already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [health_facility_dto_1.CreateHealthFacilityDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "createHealthFacility", null);
__decorate([
    (0, common_1.Get)('health-facilities'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Get all health facilities with pagination and filtering' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search term for filtering health facilities',
        example: 'Health Center',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Health facilities retrieved successfully',
        type: health_facility_dto_1.HealthFacilitiesListResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [base_facility_dto_1.PaginationQueryDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "findAllHealthFacilities", null);
__decorate([
    (0, common_1.Get)('health-facilities/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Get health facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Health facility ID',
        example: 'health_facility_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Health facility retrieved successfully',
        type: health_facility_dto_1.HealthFacilityResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Health facility not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "findOneHealthFacility", null);
__decorate([
    (0, common_1.Put)('health-facilities/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Update health facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Health facility ID',
        example: 'health_facility_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Health facility updated successfully',
        type: health_facility_dto_1.UpdateHealthFacilityResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Health facility not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Health facility number already exists' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, health_facility_dto_1.UpdateHealthFacilityDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "updateHealthFacility", null);
__decorate([
    (0, common_1.Delete)('health-facilities/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete health facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Health facility ID',
        example: 'health_facility_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Health facility deleted successfully',
        type: base_facility_dto_1.BaseDeleteResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Health facility not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "removeHealthFacility", null);
__decorate([
    (0, common_1.Post)('public-places'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new public place facility' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Public place created successfully',
        type: public_place_dto_1.CreatePublicPlaceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Public place number already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [public_place_dto_1.CreatePublicPlaceDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "createPublicPlace", null);
__decorate([
    (0, common_1.Get)('public-places'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Get all public place facilities with pagination and filtering' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search term for filtering public places',
        example: 'Market',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Public places retrieved successfully',
        type: public_place_dto_1.PublicPlacesListResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [base_facility_dto_1.PaginationQueryDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "findAllPublicPlaces", null);
__decorate([
    (0, common_1.Get)('public-places/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Get public place facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Public place ID',
        example: 'public_place_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Public place retrieved successfully',
        type: public_place_dto_1.PublicPlaceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Public place not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "findOnePublicPlace", null);
__decorate([
    (0, common_1.Put)('public-places/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Update public place facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Public place ID',
        example: 'public_place_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Public place updated successfully',
        type: public_place_dto_1.UpdatePublicPlaceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Public place not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Public place number already exists' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, public_place_dto_1.UpdatePublicPlaceDto]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "updatePublicPlace", null);
__decorate([
    (0, common_1.Delete)('public-places/:id'),
    (0, privileges_decorator_1.Privileges)(client_1.Privilege.DATA_COLLECTION),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete public place facility by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Public place ID',
        example: 'public_place_123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Public place deleted successfully',
        type: base_facility_dto_1.BaseDeleteResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Insufficient privileges' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Public place not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FacilitiesController.prototype, "removePublicPlace", null);
exports.FacilitiesController = FacilitiesController = __decorate([
    (0, swagger_1.ApiTags)('Facilities'),
    (0, common_1.Controller)('facilities'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, mobile_guard_1.MobileGuard, privilege_guard_1.PrivilegeGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [facilities_service_1.FacilitiesService])
], FacilitiesController);
//# sourceMappingURL=facilities.controller.js.map