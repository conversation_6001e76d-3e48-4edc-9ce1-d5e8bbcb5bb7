import { SettlementType } from '@prisma/client';
export declare class CreateLocationDto {
    villageId: number;
    latitude?: number;
    longitude?: number;
    settlementType?: SettlementType;
}
export declare class CreateLocationResponseDto {
    location: {
        id: string;
        villageId: number;
        village: {
            id: number;
            name: string;
            cell: {
                id: number;
                name: string;
                sector: {
                    id: number;
                    name: string;
                    district: {
                        id: number;
                        name: string;
                        province: {
                            id: number;
                            name: string;
                        };
                    };
                };
            };
        };
        latitude?: number;
        longitude?: number;
        settlementType: SettlementType;
        createdAt: Date;
        updatedAt: Date;
    };
    message: string;
}
