import { FacilityType } from '@prisma/client';
export declare class BaseCreateSubmissionDto {
    facilityId: string;
    facilityType: FacilityType;
    submittedAt?: Date;
}
export declare class BaseSubmissionResponseDto {
    id: string;
    facilityId: string;
    facilityType: FacilityType;
    submittedAt: Date;
}
export declare class BasePaginatedSubmissionResponseDto<T> {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    data: T[];
}
export declare class BaseCreateSubmissionResponseDto {
    submission: any;
    message: string;
}
