import { ApiProperty } from '@nestjs/swagger';
import { Privilege } from '@prisma/client';

export class RoleResponseDto {
  @ApiProperty({
    description: 'Role ID',
    example: 'role_123',
  })
  id: string;

  @ApiProperty({
    description: 'Role name',
    example: 'Data Collector',
  })
  name: string;

  @ApiProperty({
    description: 'Role code',
    example: 'DATA_COLLECTOR',
  })
  code: string;

  @ApiProperty({
    description: 'List of privileges assigned to this role',
    example: [Privilege.DATA_COLLECTION],
    enum: Privilege,
    isArray: true,
  })
  privileges: Privilege[];

  @ApiProperty({
    description: 'Number of users assigned to this role',
    example: 5,
  })
  userCount: number;

  @ApiProperty({
    description: 'Role creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Role last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class RolesListResponseDto {
  @ApiProperty({
    description: 'List of roles',
    type: [RoleResponseDto],
  })
  roles: RoleResponseDto[];

  @ApiProperty({
    description: 'Total number of roles',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}
