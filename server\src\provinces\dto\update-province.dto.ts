import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateProvinceDto } from './create-province.dto';

export class UpdateProvinceDto extends PartialType(CreateProvinceDto) {}

export class UpdateProvinceResponseDto {
  @ApiProperty({
    description: 'Updated province information',
  })
  province: {
    id: number;
    code: number;
    name: string;
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Province updated successfully',
  })
  message: string;
}
