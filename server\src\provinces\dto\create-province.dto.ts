import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsInt,
  MinLength,
  Min,
} from 'class-validator';

export class CreateProvinceDto {
  @ApiProperty({
    description: 'Province code (unique identifier)',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({
    description: 'Province name',
    example: 'Kigali City',
    minLength: 2,
  })
  @IsString()
  @MinLength(2)
  name: string;
}

export class CreateProvinceResponseDto {
  @ApiProperty({
    description: 'Created province information',
  })
  province: {
    id: number;
    code: number;
    name: string;
    createdAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Province created successfully',
  })
  message: string;
}
