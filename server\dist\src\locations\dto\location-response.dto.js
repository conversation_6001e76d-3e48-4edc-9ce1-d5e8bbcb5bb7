"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteLocationResponseDto = exports.LocationsListResponseDto = exports.LocationResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
class LocationResponseDto {
    id;
    villageId;
    village;
    latitude;
    longitude;
    settlementType;
    createdAt;
    updatedAt;
}
exports.LocationResponseDto = LocationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location ID',
        example: 'location_123',
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Village ID',
        example: 1,
    }),
    __metadata("design:type", Number)
], LocationResponseDto.prototype, "villageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Village information',
    }),
    __metadata("design:type", Object)
], LocationResponseDto.prototype, "village", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Latitude coordinate',
        example: -1.9441,
        required: false,
    }),
    __metadata("design:type", Number)
], LocationResponseDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Longitude coordinate',
        example: 30.0619,
        required: false,
    }),
    __metadata("design:type", Number)
], LocationResponseDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Settlement type',
        enum: client_1.SettlementType,
        example: client_1.SettlementType.URBAN,
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "settlementType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location creation date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], LocationResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location last update date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], LocationResponseDto.prototype, "updatedAt", void 0);
class LocationsListResponseDto {
    locations;
    total;
    page;
    limit;
    totalPages;
}
exports.LocationsListResponseDto = LocationsListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of locations',
        type: [LocationResponseDto],
    }),
    __metadata("design:type", Array)
], LocationsListResponseDto.prototype, "locations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of locations',
        example: 100,
    }),
    __metadata("design:type", Number)
], LocationsListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], LocationsListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], LocationsListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 10,
    }),
    __metadata("design:type", Number)
], LocationsListResponseDto.prototype, "totalPages", void 0);
class DeleteLocationResponseDto {
    message;
}
exports.DeleteLocationResponseDto = DeleteLocationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Location deleted successfully',
    }),
    __metadata("design:type", String)
], DeleteLocationResponseDto.prototype, "message", void 0);
//# sourceMappingURL=location-response.dto.js.map