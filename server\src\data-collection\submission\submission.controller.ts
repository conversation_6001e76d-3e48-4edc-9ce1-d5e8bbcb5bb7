import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { SubmissionService } from './submission.service';
import { CreateHouseholdSubmissionDto } from './dto/household.dto';
import { CreateSchoolSubmissionDto } from './dto/school.dto';
import { CreateHealthFacilitySubmissionDto } from './dto/health-facility.dto';
import { CurrentUser } from 'src/common/decorators/current-user.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { MobileGuard } from 'src/common/guards/mobile.guard';
import { PrivilegeGuard } from 'src/common/guards/privilege.guard';

@ApiTags('Submission')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
@Controller('submission')
export class SubmissionController {
  constructor(private readonly submissionService: SubmissionService) {}

  @Post('household')
  createHousehold(@Body() dto: CreateHouseholdSubmissionDto, @CurrentUser() user: any) {
    return this.submissionService.createHouseholdSubmission(dto, user.id);
  }

  @Post('school')
  createSchool(@Body() dto: CreateSchoolSubmissionDto, @CurrentUser() user: any) {
    return this.submissionService.createSchoolSubmission(dto, user.id);
  }

  @Post('health-facility')
  createHealthFacility(@Body() dto: CreateHealthFacilitySubmissionDto, @CurrentUser() user: any) {
    return this.submissionService.createHealthFacilitySubmission(dto, user.id);
  }
}
