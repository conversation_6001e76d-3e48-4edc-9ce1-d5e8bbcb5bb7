import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsInt,
  MinLength,
  Min,
} from 'class-validator';

export class CreateDistrictDto {
  @ApiProperty({
    description: 'District code (unique identifier)',
    example: 101,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({
    description: 'District name',
    example: 'Nyarugenge',
    minLength: 2,
  })
  @IsString()
  @MinLength(2)
  name: string;

  @ApiProperty({
    description: 'Province ID this district belongs to',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  provinceId: number;
}

export class CreateDistrictResponseDto {
  @ApiProperty({
    description: 'Created district information',
  })
  district: {
    id: number;
    code: number;
    name: string;
    provinceId: number;
    createdAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'District created successfully',
  })
  message: string;
}
