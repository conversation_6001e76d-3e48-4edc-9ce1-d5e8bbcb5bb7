import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { LocationsService } from './locations.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { PrivilegeGuard } from '../common/guards/privilege.guard';
import { MobileGuard } from '../common/guards/mobile.guard';
import { Privileges } from '../common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';
import { PaginationQueryDto } from '../facilities/dto/base-facility.dto';
import {
  CreateLocationDto,
  CreateLocationResponseDto,
} from './dto/create-location.dto';
import {
  UpdateLocationDto,
  UpdateLocationResponseDto,
} from './dto/update-location.dto';
import {
  LocationResponseDto,
  LocationsListResponseDto,
  DeleteLocationResponseDto,
} from './dto/location-response.dto';

@ApiTags('Locations')
@Controller('locations')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
export class LocationsController {
  constructor(private readonly locationsService: LocationsService) {}

  @Post()
  @Privileges(Privilege.DATA_COLLECTION)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new location' })
  @ApiResponse({
    status: 201,
    description: 'Location created successfully',
    type: CreateLocationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Village not found' })
  async create(@Body() createLocationDto: CreateLocationDto) {
    return this.locationsService.create(createLocationDto);
  }

  @Get()
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get all locations with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for filtering locations by village name',
    example: 'Kigali',
  })
  @ApiResponse({
    status: 200,
    description: 'Locations retrieved successfully',
    type: LocationsListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAll(@Query() query: PaginationQueryDto) {
    return this.locationsService.findAll(
      query.page,
      query.limit,
      query.search,
    );
  }

  @Get(':id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Get location by ID' })
  @ApiParam({
    name: 'id',
    description: 'Location ID',
    example: 'location_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Location retrieved successfully',
    type: LocationResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Location not found' })
  async findOne(@Param('id') id: string) {
    return this.locationsService.findOne(id);
  }

  @Put(':id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Update location by ID' })
  @ApiParam({
    name: 'id',
    description: 'Location ID',
    example: 'location_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Location updated successfully',
    type: UpdateLocationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Location not found' })
  async update(
    @Param('id') id: string,
    @Body() updateLocationDto: UpdateLocationDto,
  ) {
    return this.locationsService.update(id, updateLocationDto);
  }

  @Delete(':id')
  @Privileges(Privilege.DATA_COLLECTION)
  @ApiOperation({ summary: 'Delete location by ID' })
  @ApiParam({
    name: 'id',
    description: 'Location ID',
    example: 'location_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Location deleted successfully',
    type: DeleteLocationResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Location not found' })
  @ApiResponse({ status: 409, description: 'Location is in use by facilities' })
  async remove(@Param('id') id: string) {
    return this.locationsService.remove(id);
  }
}
