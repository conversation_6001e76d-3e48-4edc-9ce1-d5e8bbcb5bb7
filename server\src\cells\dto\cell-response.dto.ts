import { ApiProperty } from '@nestjs/swagger';

export class CellResponseDto {
  @ApiProperty({
    description: 'Cell ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Cell code',
    example: 1010101,
  })
  code: number;

  @ApiProperty({
    description: 'Cell name',
    example: '<PERSON>is<PERSON><PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Sector information',
  })
  sector: {
    id: number;
    name: string;
    code: number;
    district: {
      id: number;
      name: string;
      code: number;
      province: {
        id: number;
        name: string;
        code: number;
      };
    };
  };

  @ApiProperty({
    description: 'Number of villages in this cell',
    example: 8,
  })
  villageCount: number;

  @ApiProperty({
    description: 'Cell creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Cell last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CellsListResponseDto {
  @ApiProperty({
    description: 'List of cells',
    type: [CellResponseDto],
  })
  cells: CellResponseDto[];

  @ApiProperty({
    description: 'Total number of cells',
    example: 2148,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 215,
  })
  totalPages: number;
}
