import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateRoleDto,
  CreateRoleResponseDto,
} from './dto/create-role.dto';
import {
  UpdateRoleDto,
  UpdateRoleResponseDto,
} from './dto/update-role.dto';
import {
  RoleResponseDto,
  RolesListResponseDto,
} from './dto/role-response.dto';
import { Privilege } from '@prisma/client';

@Injectable()
export class RolesService {
  constructor(private prisma: PrismaService) {}

  async create(createRoleDto: CreateRoleDto): Promise<CreateRoleResponseDto> {
    const { name, code, privileges } = createRoleDto;

    // Check if role with same name or code already exists
    const existingRole = await this.prisma.role.findFirst({
      where: {
        OR: [
          { name },
          { code },
        ],
      },
    });

    if (existingRole) {
      if (existingRole.name === name) {
        throw new ConflictException('Role with this name already exists');
      }
      if (existingRole.code === code) {
        throw new ConflictException('Role with this code already exists');
      }
    }

    // Create role
    const role = await this.prisma.role.create({
      data: {
        name,
        code,
        privileges,
      },
    });

    return {
      role: {
        id: role.id,
        name: role.name,
        code: role.code,
        privileges: role.privileges,
        createdAt: role.createdAt,
      },
      message: 'Role created successfully',
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
  ): Promise<RolesListResponseDto> {
    const skip = (page - 1) * limit;

    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { code: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : {};

    const [roles, total] = await Promise.all([
      this.prisma.role.findMany({
        where,
        skip,
        take: limit,
        include: {
          _count: {
            select: { users: true },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.role.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      roles: roles.map((role) => ({
        id: role.id,
        name: role.name,
        code: role.code,
        privileges: role.privileges,
        userCount: role._count.users,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: string): Promise<RoleResponseDto> {
    const role = await this.prisma.role.findUnique({
      where: { id },
      include: {
        _count: {
          select: { users: true },
        },
      },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return {
      id: role.id,
      name: role.name,
      code: role.code,
      privileges: role.privileges,
      userCount: role._count.users,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    };
  }

  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<UpdateRoleResponseDto> {
    const { name, code, privileges } = updateRoleDto;

    // Check if role exists
    const existingRole = await this.prisma.role.findUnique({
      where: { id },
    });

    if (!existingRole) {
      throw new NotFoundException('Role not found');
    }

    // Check for conflicts with other roles
    if (name || code) {
      const conflictRole = await this.prisma.role.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                name ? { name } : {},
                code ? { code } : {},
              ].filter(Boolean),
            },
          ],
        },
      });

      if (conflictRole) {
        if (conflictRole.name === name) {
          throw new ConflictException('Role with this name already exists');
        }
        if (conflictRole.code === code) {
          throw new ConflictException('Role with this code already exists');
        }
      }
    }

    // Update role
    const updatedRole = await this.prisma.role.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(code && { code }),
        ...(privileges && { privileges }),
      },
    });

    return {
      role: {
        id: updatedRole.id,
        name: updatedRole.name,
        code: updatedRole.code,
        privileges: updatedRole.privileges,
        updatedAt: updatedRole.updatedAt,
      },
      message: 'Role updated successfully',
    };
  }

  async remove(id: string): Promise<{ message: string }> {
    // Check if role exists
    const existingRole = await this.prisma.role.findUnique({
      where: { id },
      include: {
        _count: {
          select: { users: true },
        },
      },
    });

    if (!existingRole) {
      throw new NotFoundException('Role not found');
    }

    // Check if role has users assigned
    if (existingRole._count.users > 0) {
      throw new BadRequestException(
        `Cannot delete role. ${existingRole._count.users} user(s) are assigned to this role`,
      );
    }

    // Delete role
    await this.prisma.role.delete({
      where: { id },
    });

    return { message: 'Role deleted successfully' };
  }

  async getPrivilleges(): Promise<Privilege[]> {
    return Object.values(Privilege);
  }
}
