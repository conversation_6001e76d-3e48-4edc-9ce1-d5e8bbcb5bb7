import { ApiProperty } from '@nestjs/swagger';
import { FacilityType } from '@prisma/client';
import { Transform } from 'class-transformer';
import { IsDate, IsEnum, IsOptional, IsString } from 'class-validator';

export class BaseCreateSubmissionDto {
    @ApiProperty({
        description: 'Facility ID',
        example: 'facility_123',
    })
    @IsString()
    facilityId: string;

    @ApiProperty({
        description: 'Facility type',
        enum: FacilityType,
        example: FacilityType.HOUSEHOLD,
    })
    @IsEnum(FacilityType)
    facilityType: FacilityType;

    @ApiProperty({
        description: 'Submission date',
        example: '2024-01-01T00:00:00.000Z',
    })
    @IsOptional()
    @IsDate()
    @Transform(({ value }) => new Date(value))
    submittedAt?: Date;

}

export class BaseSubmissionResponseDto {
    @ApiProperty({
        description: 'Submission ID',
        example: 'submission_123',
    })
    id: string;

    @ApiProperty({
        description: 'Facility ID',
        example: 'facility_123',
    })
    facilityId: string;

    @ApiProperty({
        description: 'Facility type',
        enum: FacilityType,
        example: FacilityType.HOUSEHOLD,
    })
    facilityType: FacilityType;

    @ApiProperty({
        description: 'Submission date',
        example: '2024-01-01T00:00:00.000Z',
    })
    submittedAt: Date;
}

export class BasePaginatedSubmissionResponseDto<T> {
    @ApiProperty({
        description: 'Total number of submissions',
        example: 100,
    })
    total: number;

    @ApiProperty({
        description: 'Current page number',
        example: 1,
    })
    page: number;

    @ApiProperty({
        description: 'Number of submissions per page',
        example: 10,
    })
    limit: number;

    @ApiProperty({
        description: 'Total number of pages',
        example: 10,
    })
    totalPages: number;

    data: T[];
}

export class BaseCreateSubmissionResponseDto {
    @ApiProperty({
        description: 'Created submission information',
    })
    submission: any;

    @ApiProperty({
        description: 'Success message',
        example: 'Submission created successfully',
    })
    message: string;
}