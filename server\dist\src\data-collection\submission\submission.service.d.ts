import { CreateHouseholdSubmissionDto } from './dto/household.dto';
import { CreateSchoolSubmissionDto } from './dto/school.dto';
import { CreateHealthFacilitySubmissionDto } from './dto/health-facility.dto';
import { BaseCreateSubmissionResponseDto } from './dto/base-submission.dto';
import { PrismaService } from 'src/prisma/prisma.service';
export declare class SubmissionService {
    private prisma;
    constructor(prisma: PrismaService);
    createHouseholdSubmission(dto: CreateHouseholdSubmissionDto, userId: string): Promise<BaseCreateSubmissionResponseDto>;
    createSchoolSubmission(dto: CreateSchoolSubmissionDto, userId: string): Promise<{
        message: string;
        submission: {
            id: string;
            facilityType: import("@prisma/client").$Enums.FacilityType;
            submittedById: string;
            submittedAt: Date;
            houseHoldId: string | null;
            schoolId: string | null;
            healthFacilityId: string | null;
            publicPlaceId: string | null;
        };
    }>;
    createHealthFacilitySubmission(dto: CreateHealthFacilitySubmissionDto, userId: string): Promise<{
        message: string;
        submission: {
            id: string;
            facilityType: import("@prisma/client").$Enums.FacilityType;
            submittedById: string;
            submittedAt: Date;
            houseHoldId: string | null;
            schoolId: string | null;
            healthFacilityId: string | null;
            publicPlaceId: string | null;
        };
    }>;
}
