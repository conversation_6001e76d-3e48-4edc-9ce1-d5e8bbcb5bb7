"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let LocationsService = class LocationsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createLocationDto) {
        const village = await this.prisma.village.findUnique({
            where: { id: createLocationDto.villageId },
            include: {
                cell: {
                    include: {
                        sector: {
                            include: {
                                district: {
                                    include: {
                                        province: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!village) {
            throw new common_1.NotFoundException('Village not found');
        }
        const location = await this.prisma.location.create({
            data: {
                villageId: createLocationDto.villageId,
                latitude: createLocationDto.latitude,
                longitude: createLocationDto.longitude,
                settlementType: createLocationDto.settlementType,
            },
            include: {
                village: {
                    include: {
                        cell: {
                            include: {
                                sector: {
                                    include: {
                                        district: {
                                            include: {
                                                province: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            location: this.formatLocationResponse(location),
            message: 'Location created successfully',
        };
    }
    async findAll(page = 1, limit = 10, search) {
        const skip = (page - 1) * limit;
        const where = search
            ? {
                village: {
                    name: { contains: search, mode: 'insensitive' },
                },
            }
            : {};
        const [locations, total] = await Promise.all([
            this.prisma.location.findMany({
                where,
                skip,
                take: limit,
                include: {
                    village: {
                        include: {
                            cell: {
                                include: {
                                    sector: {
                                        include: {
                                            district: {
                                                include: {
                                                    province: true,
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.location.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            locations: locations.map((location) => this.formatLocationResponse(location)),
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const location = await this.prisma.location.findUnique({
            where: { id },
            include: {
                village: {
                    include: {
                        cell: {
                            include: {
                                sector: {
                                    include: {
                                        district: {
                                            include: {
                                                province: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        return this.formatLocationResponse(location);
    }
    async update(id, updateLocationDto) {
        const existingLocation = await this.prisma.location.findUnique({
            where: { id },
        });
        if (!existingLocation) {
            throw new common_1.NotFoundException('Location not found');
        }
        if (updateLocationDto.villageId) {
            const village = await this.prisma.village.findUnique({
                where: { id: updateLocationDto.villageId },
            });
            if (!village) {
                throw new common_1.NotFoundException('Village not found');
            }
        }
        const location = await this.prisma.location.update({
            where: { id },
            data: updateLocationDto,
            include: {
                village: {
                    include: {
                        cell: {
                            include: {
                                sector: {
                                    include: {
                                        district: {
                                            include: {
                                                province: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        return {
            location: this.formatLocationResponse(location),
            message: 'Location updated successfully',
        };
    }
    async remove(id) {
        const location = await this.prisma.location.findUnique({
            where: { id },
            include: {
                houseHolds: true,
                schools: true,
                healthFacilities: true,
            },
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        const facilitiesCount = location.houseHolds.length +
            location.schools.length +
            location.healthFacilities.length;
        if (facilitiesCount > 0) {
            throw new common_1.ConflictException(`Location is in use by ${facilitiesCount} facility(ies) and cannot be deleted`);
        }
        await this.prisma.location.delete({
            where: { id },
        });
        return {
            message: 'Location deleted successfully',
        };
    }
    formatLocationResponse(location) {
        return {
            id: location.id,
            villageId: location.villageId,
            village: {
                id: location.village.id,
                name: location.village.name,
                code: location.village.code,
                cell: {
                    id: location.village.cell.id,
                    name: location.village.cell.name,
                    code: location.village.cell.code,
                    sector: {
                        id: location.village.cell.sector.id,
                        name: location.village.cell.sector.name,
                        code: location.village.cell.sector.code,
                        district: {
                            id: location.village.cell.sector.district.id,
                            name: location.village.cell.sector.district.name,
                            code: location.village.cell.sector.district.code,
                            province: {
                                id: location.village.cell.sector.district.province.id,
                                name: location.village.cell.sector.district.province.name,
                                code: location.village.cell.sector.district.province.code,
                            },
                        },
                    },
                },
            },
            latitude: location.latitude,
            longitude: location.longitude,
            settlementType: location.settlementType,
            createdAt: location.createdAt,
            updatedAt: location.updatedAt,
        };
    }
};
exports.LocationsService = LocationsService;
exports.LocationsService = LocationsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], LocationsService);
//# sourceMappingURL=locations.service.js.map