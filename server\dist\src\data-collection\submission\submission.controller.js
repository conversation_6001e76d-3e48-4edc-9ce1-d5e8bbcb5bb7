"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubmissionController = void 0;
const common_1 = require("@nestjs/common");
const submission_service_1 = require("./submission.service");
const household_dto_1 = require("./dto/household.dto");
const school_dto_1 = require("./dto/school.dto");
const health_facility_dto_1 = require("./dto/health-facility.dto");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../../common/guards/auth.guard");
const mobile_guard_1 = require("../../common/guards/mobile.guard");
const privilege_guard_1 = require("../../common/guards/privilege.guard");
let SubmissionController = class SubmissionController {
    submissionService;
    constructor(submissionService) {
        this.submissionService = submissionService;
    }
    createHousehold(dto, user) {
        return this.submissionService.createHouseholdSubmission(dto, user.id);
    }
    createSchool(dto, user) {
        return this.submissionService.createSchoolSubmission(dto, user.id);
    }
    createHealthFacility(dto, user) {
        return this.submissionService.createHealthFacilitySubmission(dto, user.id);
    }
};
exports.SubmissionController = SubmissionController;
__decorate([
    (0, common_1.Post)('household'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [household_dto_1.CreateHouseholdSubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createHousehold", null);
__decorate([
    (0, common_1.Post)('school'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [school_dto_1.CreateSchoolSubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createSchool", null);
__decorate([
    (0, common_1.Post)('health-facility'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [health_facility_dto_1.CreateHealthFacilitySubmissionDto, Object]),
    __metadata("design:returntype", void 0)
], SubmissionController.prototype, "createHealthFacility", null);
exports.SubmissionController = SubmissionController = __decorate([
    (0, swagger_1.ApiTags)('Submission'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, mobile_guard_1.MobileGuard, privilege_guard_1.PrivilegeGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('submission'),
    __metadata("design:paramtypes", [submission_service_1.SubmissionService])
], SubmissionController);
//# sourceMappingURL=submission.controller.js.map