import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, IsEnum, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { HealthFacilityType, HealthFacilityManagement, DailyPatientVolume, WaterAvailabilityFrequency, CleanWaterStorageCapacity, MainWaterSource, WaterSourceDistance, ToiletFacilityType, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement, WaterAvailability } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';

export class HealthFacilityGeneralInfoDto {
  @ApiProperty()
  @IsString()
  facilityName: string;

  @ApiProperty({ enum: HealthFacilityType })
  @IsEnum(HealthFacilityType)
  facilityType: HealthFacilityType;

  @ApiProperty({ enum: HealthFacilityManagement })
  @IsEnum(HealthFacilityManagement)
  managementType: HealthFacilityManagement;

  @ApiProperty({ enum: DailyPatientVolume })
  @IsEnum(DailyPatientVolume)
  dailyPatientVolume: DailyPatientVolume;

  @ApiProperty()
  @IsInt()
  totalStaff: number;
}

export class HealthFacilityWaterSupplyDto {
  @ApiProperty()
  @IsBoolean()
  connectedToPipeline: boolean;

  @ApiProperty({ enum: WaterAvailability })
  @IsEnum(WaterAvailability)
  waterAvailability: WaterAvailability;

  @ApiProperty({ enum: WaterAvailabilityFrequency, required: false })
  @IsOptional()
  @IsEnum(WaterAvailabilityFrequency)
  availableDays?: WaterAvailabilityFrequency;

  @ApiProperty({ enum: CleanWaterStorageCapacity })
  @IsEnum(CleanWaterStorageCapacity)
  storageCapacity: CleanWaterStorageCapacity;

  @ApiProperty({ enum: MainWaterSource, required: false })
  @IsOptional()
  @IsEnum(MainWaterSource)
  mainWaterSource?: MainWaterSource;

  @ApiProperty({ enum: WaterSourceDistance })
  @IsEnum(WaterSourceDistance)
  distanceToSource: WaterSourceDistance;
}

export class HealthFacilitySanitationDto {
  @ApiProperty({ enum: ToiletFacilityType })
  @IsEnum(ToiletFacilityType)
  toiletType: ToiletFacilityType;

  @ApiProperty({ enum: FacilitySlabConstructionMaterial })
  @IsEnum(FacilitySlabConstructionMaterial)
  @IsOptional()
  slabConstructionMaterial?: FacilitySlabConstructionMaterial;

  @ApiProperty()
  @IsInt()
  totalToilets: number;

  @ApiProperty()
  @IsBoolean()
  genderSeparation: boolean;

  @ApiProperty()
  @IsInt()
  femaleToilets: number;

  @ApiProperty()
  @IsInt()
  maleToilets: number;

  @ApiProperty()
  @IsBoolean()
  disabilityAccess: boolean;

  @ApiProperty()
  @IsBoolean()
  staffToilets: boolean;

  @ApiProperty()
  @IsBoolean()
  hasToiletFullInLast2Years: boolean;

  @ApiProperty({ enum: ExcretaManagement, required: false })
  @IsOptional()
  @IsEnum(ExcretaManagement)
  excretaManagement?: ExcretaManagement;
}

export class HealthFacilityHygieneDto {
  @ApiProperty()
  @IsBoolean()
  handwashingFacility: boolean;

  @ApiProperty({ enum: HandWashingFacilityType, required: false })
  @IsOptional()
  @IsEnum(HandWashingFacilityType)
  facilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: HandWashingMaterial, required: false })
  @IsOptional()
  @IsEnum(HandWashingMaterial)
  handwashingMaterials?: HandWashingMaterial;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  handWashingfacilityNearToilet?: boolean;

  @ApiProperty({ enum: HandWashingFacilityType, required: false })
  @IsOptional()
  @IsEnum(HandWashingFacilityType)
  toiletHandWashingFacilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: HandWashingMaterial, required: false })
  @IsOptional()
  @IsEnum(HandWashingMaterial)
  toiletHandwashingMaterials?: HandWashingMaterial;
}

export class HealthFacilitySolidWasteManagementDto {
  @ApiProperty()
  @IsBoolean()
  wasteSeparation: boolean;

  @ApiProperty({ enum: WasteManagementAfterSeparation, required: false })
  @IsOptional()
  @IsEnum(WasteManagementAfterSeparation)
  wasteManagement?: WasteManagementAfterSeparation;

  @ApiProperty({ enum: WasteTreatmentType, required: false })
  @IsOptional()
  @IsEnum(WasteTreatmentType)
  treatmentType?: WasteTreatmentType;

  @ApiProperty({ enum: WasteCollectionFrequency, required: false })
  @IsOptional()
  @IsEnum(WasteCollectionFrequency)
  collectionFrequency?: WasteCollectionFrequency;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  collectionCost?: number;
}

export class HealthFacilityLiquidWasteManagementDto {
  @ApiProperty({ enum: WasteWaterManagement })
  @IsEnum(WasteWaterManagement)
  liquidWasteManagement: WasteWaterManagement;
}

export class CreateHealthFacilitySubmissionDto extends BaseCreateSubmissionDto {
  @ApiProperty({ type: HealthFacilityGeneralInfoDto })
  @ValidateNested()
  @Type(() => HealthFacilityGeneralInfoDto)
  generalInfo: HealthFacilityGeneralInfoDto;

  @ApiProperty({ type: HealthFacilityWaterSupplyDto })
  @ValidateNested()
  @Type(() => HealthFacilityWaterSupplyDto)
  waterSupply: HealthFacilityWaterSupplyDto;

  @ApiProperty({ type: HealthFacilitySanitationDto })
  @ValidateNested()
  @Type(() => HealthFacilitySanitationDto)
  sanitation: HealthFacilitySanitationDto;

  @ApiProperty({ type: HealthFacilityHygieneDto })
  @ValidateNested()
  @Type(() => HealthFacilityHygieneDto)
  hygiene: HealthFacilityHygieneDto;

  @ApiProperty({ type: HealthFacilitySolidWasteManagementDto })
  @ValidateNested()
  @Type(() => HealthFacilitySolidWasteManagementDto)
  solidWaste: HealthFacilitySolidWasteManagementDto;

  @ApiProperty({ type: HealthFacilityLiquidWasteManagementDto })
  @ValidateNested()
  @Type(() => HealthFacilityLiquidWasteManagementDto)
  liquidWaste: HealthFacilityLiquidWasteManagementDto;
}

export class HealthFacilitySubmissionResponseDto extends BaseSubmissionResponseDto {
  @ApiProperty({ type: HealthFacilityGeneralInfoDto })
  generalInfo: HealthFacilityGeneralInfoDto;

  @ApiProperty({ type: HealthFacilityWaterSupplyDto })
  waterSupply: HealthFacilityWaterSupplyDto;

  @ApiProperty({ type: HealthFacilitySanitationDto })
  sanitation: HealthFacilitySanitationDto;

  @ApiProperty({ type: HealthFacilityHygieneDto })
  hygiene: HealthFacilityHygieneDto;

  @ApiProperty({ type: HealthFacilitySolidWasteManagementDto })
  solidWaste: HealthFacilitySolidWasteManagementDto;

  @ApiProperty({ type: HealthFacilityLiquidWasteManagementDto })
  liquidWaste: HealthFacilityLiquidWasteManagementDto;
}
