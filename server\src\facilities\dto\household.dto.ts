import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  BaseFacilityDto,
  BaseFacilityResponseDto,
  BasePaginatedResponseDto,
  BaseCreateResponseDto,
  BaseUpdateResponseDto,
  CreateLocationDataDto
} from './base-facility.dto';
import { Type } from 'class-transformer';
import { IsOptional, IsString, ValidateNested } from 'class-validator';

export class CreateHouseholdDto {
  @ApiProperty({
    description: 'Location ID where the facility is located (use this OR locationData, not both)',
    example: 'location_123',
    required: false,
  })
  @IsOptional()
  @IsString()
  locationId?: string;

  @ApiProperty({
    description: 'Location data to create a new location (use this OR locationId, not both)',
    type: CreateLocationDataDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateLocationDataDto)
  locationData?: CreateLocationDataDto;

  @ApiProperty({
    description: 'Head of household name',
    example: '<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString()
  headOfHouseholdName?: string;

  @ApiProperty({
  description: 'Facility number (unique identifier)',
  example: '1001',
  required: false,
  })
  @IsString()
  @IsOptional()
  number?: string;
}

export class UpdateHouseholdDto extends PartialType(CreateHouseholdDto) { }

export class HouseholdResponseDto extends BaseFacilityResponseDto { 
  @ApiProperty({
    description: 'Head of household name',
    example: 'John Doe',
  })
  headOfHouseholdName: string;
}

export class HouseholdsListResponseDto extends BasePaginatedResponseDto<HouseholdResponseDto> {
  @ApiProperty({
    description: 'List of households',
    type: [HouseholdResponseDto],
  })
  declare data: HouseholdResponseDto[];
}

export class CreateHouseholdResponseDto extends BaseCreateResponseDto<HouseholdResponseDto> {
  @ApiProperty({
    description: 'Created household information',
  })
  declare facility: HouseholdResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'Household created successfully',
  })
  declare message: string;
}

export class UpdateHouseholdResponseDto extends BaseUpdateResponseDto<HouseholdResponseDto> {
  @ApiProperty({
    description: 'Updated household information',
  })
  declare facility: HouseholdResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'Household updated successfully',
  })
  declare message: string;
}
