import { SettlementType } from '@prisma/client';
export declare class LocationResponseDto {
    id: string;
    villageId: number;
    village: {
        id: number;
        code: number;
        name: string;
        cell: {
            id: number;
            code: number;
            name: string;
            sector: {
                id: number;
                code: number;
                name: string;
                district: {
                    id: number;
                    code: number;
                    name: string;
                    province: {
                        id: number;
                        code: number;
                        name: string;
                    };
                };
            };
        };
    };
    latitude?: number;
    longitude?: number;
    settlementType: SettlementType;
    createdAt: Date;
    updatedAt: Date;
}
export declare class LocationsListResponseDto {
    locations: LocationResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export declare class DeleteLocationResponseDto {
    message: string;
}
