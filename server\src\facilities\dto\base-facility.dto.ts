import { ApiProperty } from '@nestjs/swagger';
import { SettlementType } from '@prisma/client';
import { IsString, IsInt, Min, IsOptional, IsNumber, IsEnum, ValidateNested, IsObject } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CreateLocationDataDto {
  @ApiProperty({
    description: 'Village ID where the location is situated',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  villageId: number;

  @ApiProperty({
    description: 'Latitude coordinate',
    example: -1.9441,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 30.0619,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiProperty({
    description: 'Settlement type',
    enum: SettlementType,
    example: SettlementType.URBAN,
    required: false,
    default: SettlementType.RURAL,
  })
  @IsOptional()
  @IsEnum(SettlementType)
  settlementType?: SettlementType = SettlementType.RURAL;
}

export class BaseFacilityDto {
  @ApiProperty({
    description: 'Location ID where the facility is located (use this OR locationData, not both)',
    example: 'location_123',
    required: false,
  })
  @IsOptional()
  @IsString()
  locationId?: string;

  @ApiProperty({
    description: 'Location data to create a new location (use this OR locationId, not both)',
    type: CreateLocationDataDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateLocationDataDto)
  locationData?: CreateLocationDataDto;

  @ApiProperty({
    description: 'Facility number (unique identifier)',
    example: '1001',
  })
  @IsString()
  @IsOptional()
  number?: string;
}

export class BaseFacilityWithNameDto extends BaseFacilityDto {
  @ApiProperty({
    description: 'Facility name',
    example: 'Central Health Center',
  })
  @IsString()
  name: string;
}

export class PaginationQueryDto {
  @ApiProperty({
    description: 'Page number',
    example: 1,
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 1;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 1 : parsed;
  })
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 10;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 10 : parsed;
  })
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({
    description: 'Village ID',
    example: 101010101,
    required: false,
  })
  @IsString()
  @IsOptional()
  villageId?: string;

  @ApiProperty({
    description: 'Search term for filtering facilities',
    example: 'health center',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}

export class BaseFacilityResponseDto {
  @ApiProperty({
    description: 'Facility ID',
    example: 'facility_123',
  })
  id: string;

  @ApiProperty({
    description: 'Facility number',
    example: 1001,
  })
  number: number;

  @ApiProperty({
    description: 'Location information',
  })
  location: {
    id: string;
    villageId: number;
    village: {
      id: number;
      code: number;
      name: string;
      cell: {
        id: number;
        code: number;
        name: string;
        sector: {
          id: number;
          code: number;
          name: string;
          district: {
            id: number;
            code: number;
            name: string;
            province: {
              id: number;
              code: number;
              name: string;
            };
          };
        };
      };
    };
    latitude?: number;
    longitude?: number;
    settlementType: SettlementType;
  };

  @ApiProperty({
    description: 'Facility creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Facility last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class BaseFacilityWithNameResponseDto extends BaseFacilityResponseDto {
  @ApiProperty({
    description: 'Facility name',
    example: 'Central Health Center',
  })
  name: string;
}

export class BasePaginatedResponseDto<T> {
  @ApiProperty({
    description: 'Total number of items',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages: number;

  data: T[];
}

export class BaseCreateResponseDto<T> {
  @ApiProperty({
    description: 'Success message',
    example: 'Facility created successfully',
  })
  message: string;

  facility: T;
}

export class BaseUpdateResponseDto<T> {
  @ApiProperty({
    description: 'Success message',
    example: 'Facility updated successfully',
  })
  message: string;

  facility: T;
}

export class BaseDeleteResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Facility deleted successfully',
  })
  message: string;
}
