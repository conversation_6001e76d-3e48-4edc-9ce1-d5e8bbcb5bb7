"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoFactorRequiredDto = exports.LoginResponseDto = exports.LoginWith2FADto = exports.LoginDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class LoginDto {
    email;
    password;
}
exports.LoginDto = LoginDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User email address',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], LoginDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User password',
        example: 'SecurePassword123!',
        minLength: 8,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(8),
    __metadata("design:type", String)
], LoginDto.prototype, "password", void 0);
class LoginWith2FADto extends LoginDto {
    totpCode;
}
exports.LoginWith2FADto = LoginWith2FADto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '6-digit TOTP code from authenticator app',
        example: '123456',
        minLength: 6,
        maxLength: 6,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    __metadata("design:type", String)
], LoginWith2FADto.prototype, "totpCode", void 0);
class LoginResponseDto {
    accessToken;
    refreshToken;
    user;
    twoFactorEnabled;
}
exports.LoginResponseDto = LoginResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'JWT access token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    __metadata("design:type", String)
], LoginResponseDto.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'JWT refresh token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    __metadata("design:type", String)
], LoginResponseDto.prototype, "refreshToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User information',
    }),
    __metadata("design:type", Object)
], LoginResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates if 2FA is enabled',
        example: true,
    }),
    __metadata("design:type", Boolean)
], LoginResponseDto.prototype, "twoFactorEnabled", void 0);
class TwoFactorRequiredDto {
    requires2FA;
    tempToken;
    message;
}
exports.TwoFactorRequiredDto = TwoFactorRequiredDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates that 2FA is required',
        example: true,
    }),
    __metadata("design:type", Boolean)
], TwoFactorRequiredDto.prototype, "requires2FA", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Temporary token for 2FA verification',
        example: 'temp_token_123',
    }),
    __metadata("design:type", String)
], TwoFactorRequiredDto.prototype, "tempToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message to display to user',
        example: 'Please enter your 2FA code',
    }),
    __metadata("design:type", String)
], TwoFactorRequiredDto.prototype, "message", void 0);
//# sourceMappingURL=login.dto.js.map