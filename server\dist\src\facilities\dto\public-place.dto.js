"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePublicPlaceResponseDto = exports.CreatePublicPlaceResponseDto = exports.PublicPlacesListResponseDto = exports.PublicPlaceResponseDto = exports.UpdatePublicPlaceDto = exports.CreatePublicPlaceDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const base_facility_dto_1 = require("./base-facility.dto");
const client_1 = require("@prisma/client");
const class_validator_1 = require("class-validator");
class CreatePublicPlaceDto extends base_facility_dto_1.BaseFacilityWithNameDto {
    type;
}
exports.CreatePublicPlaceDto = CreatePublicPlaceDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'PublicPlace type',
        enum: client_1.PublicPlaceType,
        example: client_1.PublicPlaceType.MARKET,
    }),
    (0, class_validator_1.IsEnum)(client_1.PublicPlaceType),
    __metadata("design:type", String)
], CreatePublicPlaceDto.prototype, "type", void 0);
class UpdatePublicPlaceDto extends (0, swagger_1.PartialType)(CreatePublicPlaceDto) {
}
exports.UpdatePublicPlaceDto = UpdatePublicPlaceDto;
class PublicPlaceResponseDto extends base_facility_dto_1.BaseFacilityWithNameResponseDto {
    type;
}
exports.PublicPlaceResponseDto = PublicPlaceResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'PublicPlace type',
        enum: client_1.PublicPlaceType,
        example: client_1.PublicPlaceType.MARKET,
    }),
    __metadata("design:type", String)
], PublicPlaceResponseDto.prototype, "type", void 0);
class PublicPlacesListResponseDto extends base_facility_dto_1.BasePaginatedResponseDto {
}
exports.PublicPlacesListResponseDto = PublicPlacesListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of PublicPlaces',
        type: [PublicPlaceResponseDto],
    }),
    __metadata("design:type", Array)
], PublicPlacesListResponseDto.prototype, "data", void 0);
class CreatePublicPlaceResponseDto extends base_facility_dto_1.BaseCreateResponseDto {
}
exports.CreatePublicPlaceResponseDto = CreatePublicPlaceResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created PublicPlace information',
    }),
    __metadata("design:type", PublicPlaceResponseDto)
], CreatePublicPlaceResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'PublicPlace created successfully',
    }),
    __metadata("design:type", String)
], CreatePublicPlaceResponseDto.prototype, "message", void 0);
class UpdatePublicPlaceResponseDto extends base_facility_dto_1.BaseUpdateResponseDto {
}
exports.UpdatePublicPlaceResponseDto = UpdatePublicPlaceResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Updated PublicPlace information',
    }),
    __metadata("design:type", PublicPlaceResponseDto)
], UpdatePublicPlaceResponseDto.prototype, "facility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'PublicPlace updated successfully',
    }),
    __metadata("design:type", String)
], UpdatePublicPlaceResponseDto.prototype, "message", void 0);
//# sourceMappingURL=public-place.dto.js.map