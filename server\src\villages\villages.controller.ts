import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { VillagesService } from './villages.service';
import { AuthGuard } from '../common/guards/auth.guard';
import { PrivilegeGuard } from '../common/guards/privilege.guard';
import { MobileGuard } from '../common/guards/mobile.guard';
import { Privileges } from '../common/decorators/privileges.decorator';
import { Privilege } from '@prisma/client';
import {
  CreateVillageDto,
  CreateVillageResponseDto,
} from './dto/create-village.dto';
import {
  UpdateVillageDto,
  UpdateVillageResponseDto,
} from './dto/update-village.dto';
import {
  VillageResponseDto,
  VillagesListResponseDto,
} from './dto/village-response.dto';

@ApiTags('Villages')
@Controller('villages')
@UseGuards(AuthGuard, MobileGuard, PrivilegeGuard)
@ApiBearerAuth()
export class VillagesController {
  constructor(private readonly villagesService: VillagesService) {}

  @Post()
  @Privileges(Privilege.USER_MANAGEMENT)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new village' })
  @ApiResponse({
    status: 201,
    description: 'Village created successfully',
    type: CreateVillageResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Cell not found' })
  @ApiResponse({ status: 409, description: 'Village already exists' })
  async create(@Body() createVillageDto: CreateVillageDto) {
    return this.villagesService.create(createVillageDto);
  }

  @Get()
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get all villages with pagination and filtering' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by village name or code',
    example: 'Ubumwe',
  })
  @ApiQuery({
    name: 'cellId',
    required: false,
    type: Number,
    description: 'Filter by cell ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Villages retrieved successfully',
    type: VillagesListResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('search') search?: string,
    @Query('cellId', new ParseIntPipe({ optional: true })) cellId?: number,
  ) {
    return this.villagesService.findAll(page, limit, search, cellId);
  }

  @Get(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Get a village by ID' })
  @ApiParam({
    name: 'id',
    description: 'Village ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Village retrieved successfully',
    type: VillageResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Village not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.villagesService.findOne(id);
  }

  @Patch(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Update a village' })
  @ApiParam({
    name: 'id',
    description: 'Village ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Village updated successfully',
    type: UpdateVillageResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Village or Cell not found' })
  @ApiResponse({ status: 409, description: 'Village already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateVillageDto: UpdateVillageDto) {
    return this.villagesService.update(id, updateVillageDto);
  }

  @Delete(':id')
  @Privileges(Privilege.USER_MANAGEMENT)
  @ApiOperation({ summary: 'Delete a village' })
  @ApiParam({
    name: 'id',
    description: 'Village ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Village deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Village deleted successfully',
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient privileges' })
  @ApiResponse({ status: 404, description: 'Village not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.villagesService.remove(id);
  }
}
