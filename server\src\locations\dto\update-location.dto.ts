import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateLocationDto } from './create-location.dto';
import { SettlementType } from '@prisma/client';

export class UpdateLocationDto extends PartialType(CreateLocationDto) {}

export class UpdateLocationResponseDto {
  @ApiProperty({
    description: 'Updated location information',
  })
  location: {
    id: string;
    villageId: number;
    village: {
      id: number;
      name: string;
      cell: {
        id: number;
        name: string;
        sector: {
          id: number;
          name: string;
          district: {
            id: number;
            name: string;
            province: {
              id: number;
              name: string;
            };
          };
        };
      };
    };
    latitude?: number;
    longitude?: number;
    settlementType: SettlementType;
    createdAt: Date;
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Location updated successfully',
  })
  message: string;
}
