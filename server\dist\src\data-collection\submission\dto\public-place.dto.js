"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketSubmissionResponseDto = exports.CreateMarketSubmissionDto = exports.MarketLiquidWasteManagementDto = exports.MarketSolidWasteManagementDto = exports.PublicPlaceHygieneDto = exports.PublicPlaceSanitationDto = exports.PublicPlaceWaterSupplyDto = exports.PublicPlaceGeneralInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
const base_submission_dto_1 = require("./base-submission.dto");
class PublicPlaceGeneralInfoDto {
    name;
    marketCategory;
    openingDays;
}
exports.PublicPlaceGeneralInfoDto = PublicPlaceGeneralInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PublicPlaceGeneralInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MarketCategory }),
    (0, class_validator_1.IsEnum)(client_1.MarketCategory),
    __metadata("design:type", String)
], PublicPlaceGeneralInfoDto.prototype, "marketCategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MarketOpeningDays }),
    (0, class_validator_1.IsEnum)(client_1.MarketOpeningDays),
    __metadata("design:type", String)
], PublicPlaceGeneralInfoDto.prototype, "openingDays", void 0);
class PublicPlaceWaterSupplyDto {
    connectedToPipeline;
    waterAvailability;
    availableDays;
    storageCapacity;
    mainWaterSource;
    distanceToSource;
}
exports.PublicPlaceWaterSupplyDto = PublicPlaceWaterSupplyDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceWaterSupplyDto.prototype, "connectedToPipeline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailability }),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailability),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "waterAvailability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailabilityFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailabilityFrequency),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "availableDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.CleanWaterStorageCapacity }),
    (0, class_validator_1.IsEnum)(client_1.CleanWaterStorageCapacity),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "storageCapacity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MainWaterSource, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MainWaterSource),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "mainWaterSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterSourceDistance }),
    (0, class_validator_1.IsEnum)(client_1.WaterSourceDistance),
    __metadata("design:type", String)
], PublicPlaceWaterSupplyDto.prototype, "distanceToSource", void 0);
class PublicPlaceSanitationDto {
    toiletType;
    slabConstructionMaterial;
    totalToilets;
    genderSeparation;
    femaleToilets;
    maleToilets;
    girlsRoom;
    disabilityAccess;
    staffToilets;
    hasToiletFullInLast2Years;
    excretaManagement;
}
exports.PublicPlaceSanitationDto = PublicPlaceSanitationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ToiletFacilityType }),
    (0, class_validator_1.IsEnum)(client_1.ToiletFacilityType),
    __metadata("design:type", String)
], PublicPlaceSanitationDto.prototype, "toiletType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilitySlabConstructionMaterial }),
    (0, class_validator_1.IsEnum)(client_1.FacilitySlabConstructionMaterial),
    __metadata("design:type", String)
], PublicPlaceSanitationDto.prototype, "slabConstructionMaterial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PublicPlaceSanitationDto.prototype, "totalToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSanitationDto.prototype, "genderSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PublicPlaceSanitationDto.prototype, "femaleToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PublicPlaceSanitationDto.prototype, "maleToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSanitationDto.prototype, "girlsRoom", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSanitationDto.prototype, "disabilityAccess", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSanitationDto.prototype, "staffToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceSanitationDto.prototype, "hasToiletFullInLast2Years", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ExcretaManagement, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ExcretaManagement),
    __metadata("design:type", String)
], PublicPlaceSanitationDto.prototype, "excretaManagement", void 0);
class PublicPlaceHygieneDto {
    handwashingFacility;
    facilityType;
    handwashingMaterials;
    handWashingfacilityNearToilet;
    toiletHandWashingFacilityType;
    toiletHandWashingMaterials;
}
exports.PublicPlaceHygieneDto = PublicPlaceHygieneDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceHygieneDto.prototype, "handwashingFacility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    __metadata("design:type", String)
], PublicPlaceHygieneDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MarketHandWashingMaterial, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MarketHandWashingMaterial),
    __metadata("design:type", String)
], PublicPlaceHygieneDto.prototype, "handwashingMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PublicPlaceHygieneDto.prototype, "handWashingfacilityNearToilet", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    __metadata("design:type", String)
], PublicPlaceHygieneDto.prototype, "toiletHandWashingFacilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingMaterial, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingMaterial),
    __metadata("design:type", String)
], PublicPlaceHygieneDto.prototype, "toiletHandWashingMaterials", void 0);
class MarketSolidWasteManagementDto {
    wasteSeparation;
    wasteManagement;
    treatmentType;
    collectionFrequency;
    collectionCost;
}
exports.MarketSolidWasteManagementDto = MarketSolidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], MarketSolidWasteManagementDto.prototype, "wasteSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteManagementAfterSeparation, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteManagementAfterSeparation),
    __metadata("design:type", String)
], MarketSolidWasteManagementDto.prototype, "wasteManagement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteTreatmentType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteTreatmentType),
    __metadata("design:type", String)
], MarketSolidWasteManagementDto.prototype, "treatmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteCollectionFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteCollectionFrequency),
    __metadata("design:type", String)
], MarketSolidWasteManagementDto.prototype, "collectionFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], MarketSolidWasteManagementDto.prototype, "collectionCost", void 0);
class MarketLiquidWasteManagementDto {
    liquidWasteManagement;
}
exports.MarketLiquidWasteManagementDto = MarketLiquidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteWaterManagement }),
    (0, class_validator_1.IsEnum)(client_1.WasteWaterManagement),
    __metadata("design:type", String)
], MarketLiquidWasteManagementDto.prototype, "liquidWasteManagement", void 0);
class CreateMarketSubmissionDto extends base_submission_dto_1.BaseCreateSubmissionDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.CreateMarketSubmissionDto = CreateMarketSubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceGeneralInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceGeneralInfoDto),
    __metadata("design:type", PublicPlaceGeneralInfoDto)
], CreateMarketSubmissionDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceWaterSupplyDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceWaterSupplyDto),
    __metadata("design:type", PublicPlaceWaterSupplyDto)
], CreateMarketSubmissionDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceSanitationDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceSanitationDto),
    __metadata("design:type", PublicPlaceSanitationDto)
], CreateMarketSubmissionDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceHygieneDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PublicPlaceHygieneDto),
    __metadata("design:type", PublicPlaceHygieneDto)
], CreateMarketSubmissionDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: MarketSolidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => MarketSolidWasteManagementDto),
    __metadata("design:type", MarketSolidWasteManagementDto)
], CreateMarketSubmissionDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: MarketLiquidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => MarketLiquidWasteManagementDto),
    __metadata("design:type", MarketLiquidWasteManagementDto)
], CreateMarketSubmissionDto.prototype, "liquidWaste", void 0);
class MarketSubmissionResponseDto extends base_submission_dto_1.BaseSubmissionResponseDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.MarketSubmissionResponseDto = MarketSubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceGeneralInfoDto }),
    __metadata("design:type", PublicPlaceGeneralInfoDto)
], MarketSubmissionResponseDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceWaterSupplyDto }),
    __metadata("design:type", PublicPlaceWaterSupplyDto)
], MarketSubmissionResponseDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceSanitationDto }),
    __metadata("design:type", PublicPlaceSanitationDto)
], MarketSubmissionResponseDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: PublicPlaceHygieneDto }),
    __metadata("design:type", PublicPlaceHygieneDto)
], MarketSubmissionResponseDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: MarketSolidWasteManagementDto }),
    __metadata("design:type", MarketSolidWasteManagementDto)
], MarketSubmissionResponseDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: MarketLiquidWasteManagementDto }),
    __metadata("design:type", MarketLiquidWasteManagementDto)
], MarketSubmissionResponseDto.prototype, "liquidWaste", void 0);
//# sourceMappingURL=public-place.dto.js.map