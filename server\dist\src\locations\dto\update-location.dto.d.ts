import { CreateLocationDto } from './create-location.dto';
import { SettlementType } from '@prisma/client';
declare const UpdateLocationDto_base: import("@nestjs/common").Type<Partial<CreateLocationDto>>;
export declare class UpdateLocationDto extends UpdateLocationDto_base {
}
export declare class UpdateLocationResponseDto {
    location: {
        id: string;
        villageId: number;
        village: {
            id: number;
            name: string;
            cell: {
                id: number;
                name: string;
                sector: {
                    id: number;
                    name: string;
                    district: {
                        id: number;
                        name: string;
                        province: {
                            id: number;
                            name: string;
                        };
                    };
                };
            };
        };
        latitude?: number;
        longitude?: number;
        settlementType: SettlementType;
        createdAt: Date;
        updatedAt: Date;
    };
    message: string;
}
export {};
