FROM node:18-alpine

RUN npm install -g pnpm

WORKDIR /app

# Copy package files
COPY pnpm-lock.yaml package.json ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code and configuration files
COPY . .

# Generate Prisma client
RUN pnpm db:generate

# Build the application
RUN pnpm build

# Expose the port the app runs on
EXPOSE 8080

# Start the application
CMD ["node", "dist/src/main"]
