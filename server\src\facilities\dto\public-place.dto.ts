import { ApiProperty, PartialType } from '@nestjs/swagger';
import { 
  BaseFacilityWithNameDto, 
  BaseFacilityWithNameResponseDto, 
  BasePaginatedResponseDto,
  BaseCreateResponseDto,
  BaseUpdateResponseDto
} from './base-facility.dto';
import { PublicPlaceType } from '@prisma/client';
import { IsEnum } from 'class-validator';

export class CreatePublicPlaceDto extends BaseFacilityWithNameDto {
  @ApiProperty({
    description: 'PublicPlace type',
    enum: PublicPlaceType,
    example: PublicPlaceType.MARKET,
  })
  @IsEnum(PublicPlaceType)
  type: PublicPlaceType;
}

export class UpdatePublicPlaceDto extends PartialType(CreatePublicPlaceDto) {}

export class PublicPlaceResponseDto extends BaseFacilityWithNameResponseDto {
  @ApiProperty({
    description: 'PublicPlace type',
    enum: PublicPlaceType,
    example: PublicPlaceType.MARKET,
  })
  type: PublicPlaceType;
}

export class PublicPlacesListResponseDto extends BasePaginatedResponseDto<PublicPlaceResponseDto> {
  @ApiProperty({
    description: 'List of PublicPlaces',
    type: [PublicPlaceResponseDto],
  })
  declare data: PublicPlaceResponseDto[];
}

export class CreatePublicPlaceResponseDto extends BaseCreateResponseDto<PublicPlaceResponseDto> {
  @ApiProperty({
    description: 'Created PublicPlace information',
  })
  declare facility: PublicPlaceResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'PublicPlace created successfully',
  })
  declare message: string;
}

export class UpdatePublicPlaceResponseDto extends BaseUpdateResponseDto<PublicPlaceResponseDto> {
  @ApiProperty({
    description: 'Updated PublicPlace information',
  })
  declare facility: PublicPlaceResponseDto;

  @ApiProperty({
    description: 'Success message',
    example: 'PublicPlace updated successfully',
  })
  declare message: string;
}
