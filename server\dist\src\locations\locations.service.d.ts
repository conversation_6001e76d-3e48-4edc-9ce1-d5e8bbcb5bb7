import { PrismaService } from '../prisma/prisma.service';
import { CreateLocationDto, CreateLocationResponseDto } from './dto/create-location.dto';
import { UpdateLocationDto, UpdateLocationResponseDto } from './dto/update-location.dto';
import { LocationResponseDto, LocationsListResponseDto, DeleteLocationResponseDto } from './dto/location-response.dto';
export declare class LocationsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createLocationDto: CreateLocationDto): Promise<CreateLocationResponseDto>;
    findAll(page?: number, limit?: number, search?: string): Promise<LocationsListResponseDto>;
    findOne(id: string): Promise<LocationResponseDto>;
    update(id: string, updateLocationDto: UpdateLocationDto): Promise<UpdateLocationResponseDto>;
    remove(id: string): Promise<DeleteLocationResponseDto>;
    private formatLocationResponse;
}
