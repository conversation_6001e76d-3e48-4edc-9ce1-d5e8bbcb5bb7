import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsInt,
  MinLength,
  Min,
} from 'class-validator';

export class CreateVillageDto {
  @ApiProperty({
    description: 'Village code (unique identifier)',
    example: 101010101,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({
    description: 'Village name',
    example: 'Ubumwe',
    minLength: 2,
  })
  @IsString()
  @MinLength(2)
  name: string;

  @ApiProperty({
    description: 'Cell ID this village belongs to',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  cellId: number;
}

export class CreateVillageResponseDto {
  @ApiProperty({
    description: 'Created village information',
  })
  village: {
    id: number;
    code: number;
    name: string;
    cellId: number;
    createdAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Village created successfully',
  })
  message: string;
}
