import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsInt,
  MinLength,
  Min,
} from 'class-validator';

export class CreateSectorDto {
  @ApiProperty({
    description: 'Sector code (unique identifier)',
    example: 10101,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({
    description: 'Sector name',
    example: 'Gitega',
    minLength: 2,
  })
  @IsString()
  @MinLength(2)
  name: string;

  @ApiProperty({
    description: 'District ID this sector belongs to',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  districtId: number;
}

export class CreateSectorResponseDto {
  @ApiProperty({
    description: 'Created sector information',
  })
  sector: {
    id: number;
    code: number;
    name: string;
    districtId: number;
    createdAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Sector created successfully',
  })
  message: string;
}
