import { LocationsService } from './locations.service';
import { PaginationQueryDto } from '../facilities/dto/base-facility.dto';
import { CreateLocationDto, CreateLocationResponseDto } from './dto/create-location.dto';
import { UpdateLocationDto, UpdateLocationResponseDto } from './dto/update-location.dto';
import { LocationResponseDto, LocationsListResponseDto, DeleteLocationResponseDto } from './dto/location-response.dto';
export declare class LocationsController {
    private readonly locationsService;
    constructor(locationsService: LocationsService);
    create(createLocationDto: CreateLocationDto): Promise<CreateLocationResponseDto>;
    findAll(query: PaginationQueryDto): Promise<LocationsListResponseDto>;
    findOne(id: string): Promise<LocationResponseDto>;
    update(id: string, updateLocationDto: UpdateLocationDto): Promise<UpdateLocationResponseDto>;
    remove(id: string): Promise<DeleteLocationResponseDto>;
}
