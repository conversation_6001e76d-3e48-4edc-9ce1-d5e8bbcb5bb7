"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCreateSubmissionResponseDto = exports.BasePaginatedSubmissionResponseDto = exports.BaseSubmissionResponseDto = exports.BaseCreateSubmissionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class BaseCreateSubmissionDto {
    facilityId;
    facilityType;
    submittedAt;
}
exports.BaseCreateSubmissionDto = BaseCreateSubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility ID',
        example: 'facility_123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BaseCreateSubmissionDto.prototype, "facilityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility type',
        enum: client_1.FacilityType,
        example: client_1.FacilityType.HOUSEHOLD,
    }),
    (0, class_validator_1.IsEnum)(client_1.FacilityType),
    __metadata("design:type", String)
], BaseCreateSubmissionDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Submission date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Transform)(({ value }) => new Date(value)),
    __metadata("design:type", Date)
], BaseCreateSubmissionDto.prototype, "submittedAt", void 0);
class BaseSubmissionResponseDto {
    id;
    facilityId;
    facilityType;
    submittedAt;
}
exports.BaseSubmissionResponseDto = BaseSubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Submission ID',
        example: 'submission_123',
    }),
    __metadata("design:type", String)
], BaseSubmissionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility ID',
        example: 'facility_123',
    }),
    __metadata("design:type", String)
], BaseSubmissionResponseDto.prototype, "facilityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Facility type',
        enum: client_1.FacilityType,
        example: client_1.FacilityType.HOUSEHOLD,
    }),
    __metadata("design:type", String)
], BaseSubmissionResponseDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Submission date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], BaseSubmissionResponseDto.prototype, "submittedAt", void 0);
class BasePaginatedSubmissionResponseDto {
    total;
    page;
    limit;
    totalPages;
    data;
}
exports.BasePaginatedSubmissionResponseDto = BasePaginatedSubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of submissions',
        example: 100,
    }),
    __metadata("design:type", Number)
], BasePaginatedSubmissionResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], BasePaginatedSubmissionResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of submissions per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], BasePaginatedSubmissionResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 10,
    }),
    __metadata("design:type", Number)
], BasePaginatedSubmissionResponseDto.prototype, "totalPages", void 0);
class BaseCreateSubmissionResponseDto {
    submission;
    message;
}
exports.BaseCreateSubmissionResponseDto = BaseCreateSubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Created submission information',
    }),
    __metadata("design:type", Object)
], BaseCreateSubmissionResponseDto.prototype, "submission", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Submission created successfully',
    }),
    __metadata("design:type", String)
], BaseCreateSubmissionResponseDto.prototype, "message", void 0);
//# sourceMappingURL=base-submission.dto.js.map