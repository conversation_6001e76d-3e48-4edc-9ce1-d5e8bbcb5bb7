import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateDistrictDto } from './create-district.dto';

export class UpdateDistrictDto extends PartialType(CreateDistrictDto) {}

export class UpdateDistrictResponseDto {
  @ApiProperty({
    description: 'Updated district information',
  })
  district: {
    id: number;
    code: number;
    name: string;
    provinceId: number;
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'District updated successfully',
  })
  message: string;
}
