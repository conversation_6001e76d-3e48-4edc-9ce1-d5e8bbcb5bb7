"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubmissionService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let SubmissionService = class SubmissionService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createHouseholdSubmission(dto, userId) {
        const household = await this.prisma.houseHold.findUnique({
            where: { id: dto.facilityId },
        });
        if (!household || household.deleted) {
            throw new common_1.NotFoundException('Household not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        houseHoldId: dto.facilityId,
                        submittedById: userId,
                        HouseHoldGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        HouseHoldWaterSupply: {
                            create: dto.waterSupply,
                        },
                        HouseHoldSanitation: {
                            create: dto.sanitation,
                        },
                        HouseHoldHygiene: {
                            create: dto.hygiene,
                        },
                        HouseHoldSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        HouseHoldLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                });
                return submission;
            });
            return {
                message: 'Household submission created',
                submission: result,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to create household submission');
        }
    }
    async createSchoolSubmission(dto, userId) {
        const school = await this.prisma.school.findUnique({
            where: { id: dto.facilityId },
        });
        if (!school || school.deleted) {
            throw new common_1.NotFoundException('School not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        schoolId: dto.facilityId,
                        submittedById: userId,
                        SchoolGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        SchoolWaterSupply: {
                            create: dto.waterSupply,
                        },
                        SchoolSanitation: {
                            create: dto.sanitation,
                        },
                        SchoolHygiene: {
                            create: dto.hygiene,
                        },
                        SchoolSolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        SchoolLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                });
                return submission;
            });
            return {
                message: 'School submission created',
                submission: result,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to create school submission');
        }
    }
    async createHealthFacilitySubmission(dto, userId) {
        const healthFacility = await this.prisma.healthFacility.findUnique({
            where: { id: dto.facilityId },
        });
        if (!healthFacility || healthFacility.deleted) {
            throw new common_1.NotFoundException('Health facility not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const submission = await tx.submission.create({
                    data: {
                        facilityType: dto.facilityType,
                        submittedAt: dto.submittedAt,
                        healthFacilityId: dto.facilityId,
                        submittedById: userId,
                        HealthFacilityGeneralInfo: {
                            create: dto.generalInfo,
                        },
                        HealthFacilityWaterSupply: {
                            create: dto.waterSupply,
                        },
                        HealthFacilitySanitation: {
                            create: dto.sanitation,
                        },
                        HealthFacilityHygiene: {
                            create: dto.hygiene,
                        },
                        HealthFacilitySolidWasteManagement: {
                            create: dto.solidWaste,
                        },
                        HealthFacilityLiquidWasteManagement: {
                            create: dto.liquidWaste,
                        },
                    }
                });
                return submission;
            });
            return {
                message: 'Health facility submission created',
                submission: result,
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Failed to create health facility submission');
        }
    }
};
exports.SubmissionService = SubmissionService;
exports.SubmissionService = SubmissionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SubmissionService);
//# sourceMappingURL=submission.service.js.map