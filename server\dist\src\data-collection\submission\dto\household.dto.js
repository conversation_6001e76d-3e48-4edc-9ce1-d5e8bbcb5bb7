"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HouseholdSubmissionResponseDto = exports.CreateHouseholdSubmissionDto = exports.HouseholdLiquidWasteManagementDto = exports.HouseholdSolidWasteManagementDto = exports.HouseholdHygieneDto = exports.HouseholdSanitationDto = exports.HouseholdWaterSupplyDto = exports.HouseholdGeneralInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
const base_submission_dto_1 = require("./base-submission.dto");
class HouseholdGeneralInfoDto {
    headOfHouseholdName;
    genderOfHead;
    dateOfBirthOfHead;
    educationLevelOfHead;
    householdSize;
    childrenUnder18;
    personsWithDisabilities;
}
exports.HouseholdGeneralInfoDto = HouseholdGeneralInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HouseholdGeneralInfoDto.prototype, "headOfHouseholdName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.Gender }),
    (0, class_validator_1.IsEnum)(client_1.Gender),
    __metadata("design:type", String)
], HouseholdGeneralInfoDto.prototype, "genderOfHead", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Transform)(({ value }) => new Date(value)),
    __metadata("design:type", Date)
], HouseholdGeneralInfoDto.prototype, "dateOfBirthOfHead", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.EducationLevel }),
    (0, class_validator_1.IsEnum)(client_1.EducationLevel),
    __metadata("design:type", String)
], HouseholdGeneralInfoDto.prototype, "educationLevelOfHead", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HouseholdGeneralInfoDto.prototype, "householdSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HouseholdGeneralInfoDto.prototype, "childrenUnder18", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HouseholdGeneralInfoDto.prototype, "personsWithDisabilities", void 0);
class HouseholdWaterSupplyDto {
    waterSource;
    waterAvailability;
    availableDays;
    averageWaterCost;
    storageCapacity;
    distanceToSource;
    timeToFetch;
    jerryCanPrice;
    unimprovedReason;
    pwsNonFunctionalityReason;
    pwsNonFunctionalityDuration;
}
exports.HouseholdWaterSupplyDto = HouseholdWaterSupplyDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MainWaterSource }),
    (0, class_validator_1.IsEnum)(client_1.MainWaterSource),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "waterSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailability }),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailability),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "waterAvailability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailabilityFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailabilityFrequency),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "availableDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HouseholdWaterSupplyDto.prototype, "averageWaterCost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.CleanWaterStorageCapacity }),
    (0, class_validator_1.IsEnum)(client_1.CleanWaterStorageCapacity),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "storageCapacity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterSourceDistance }),
    (0, class_validator_1.IsEnum)(client_1.WaterSourceDistance),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "distanceToSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterFetchingTime, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WaterFetchingTime),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "timeToFetch", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], HouseholdWaterSupplyDto.prototype, "jerryCanPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.UnimprovedWaterReason, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.UnimprovedWaterReason),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "unimprovedReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.PwsNonFunctionalityReason, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.PwsNonFunctionalityReason),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "pwsNonFunctionalityReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.PwsNonFunctionalityDuration, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.PwsNonFunctionalityDuration),
    __metadata("design:type", String)
], HouseholdWaterSupplyDto.prototype, "pwsNonFunctionalityDuration", void 0);
class HouseholdSanitationDto {
    toiletType;
    toiletCompleteness;
    slabConstructionMaterial;
    hasToiletFullInLast2Years;
    toiletShared;
    excretaManagement;
}
exports.HouseholdSanitationDto = HouseholdSanitationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ToiletFacilityType }),
    (0, class_validator_1.IsEnum)(client_1.ToiletFacilityType),
    __metadata("design:type", String)
], HouseholdSanitationDto.prototype, "toiletType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ToiletFacilityCompleteness }),
    (0, class_validator_1.IsEnum)(client_1.ToiletFacilityCompleteness),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdSanitationDto.prototype, "toiletCompleteness", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilitySlabConstructionMaterial }),
    (0, class_validator_1.IsEnum)(client_1.FacilitySlabConstructionMaterial),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdSanitationDto.prototype, "slabConstructionMaterial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], HouseholdSanitationDto.prototype, "hasToiletFullInLast2Years", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], HouseholdSanitationDto.prototype, "toiletShared", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ExcretaManagement, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ExcretaManagement),
    __metadata("design:type", String)
], HouseholdSanitationDto.prototype, "excretaManagement", void 0);
class HouseholdHygieneDto {
    handwashingFacility;
    handWashingFacilityType;
    handwashingMaterials;
}
exports.HouseholdHygieneDto = HouseholdHygieneDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HouseholdHygieneDto.prototype, "handwashingFacility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType }),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdHygieneDto.prototype, "handWashingFacilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingMaterial }),
    (0, class_validator_1.IsEnum)(client_1.HandWashingMaterial),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdHygieneDto.prototype, "handwashingMaterials", void 0);
class HouseholdSolidWasteManagementDto {
    wasteSeparation;
    wasteManagement;
    treatmentType;
    collectionFrequency;
}
exports.HouseholdSolidWasteManagementDto = HouseholdSolidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HouseholdSolidWasteManagementDto.prototype, "wasteSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteManagementAfterSeparation }),
    (0, class_validator_1.IsEnum)(client_1.WasteManagementAfterSeparation),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdSolidWasteManagementDto.prototype, "wasteManagement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteTreatmentType }),
    (0, class_validator_1.IsEnum)(client_1.WasteTreatmentType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdSolidWasteManagementDto.prototype, "treatmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteCollectionFrequency }),
    (0, class_validator_1.IsEnum)(client_1.WasteCollectionFrequency),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], HouseholdSolidWasteManagementDto.prototype, "collectionFrequency", void 0);
class HouseholdLiquidWasteManagementDto {
    wasterWaterManagement;
}
exports.HouseholdLiquidWasteManagementDto = HouseholdLiquidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteWaterManagement }),
    (0, class_validator_1.IsEnum)(client_1.WasteWaterManagement),
    __metadata("design:type", String)
], HouseholdLiquidWasteManagementDto.prototype, "wasterWaterManagement", void 0);
class CreateHouseholdSubmissionDto extends base_submission_dto_1.BaseCreateSubmissionDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.CreateHouseholdSubmissionDto = CreateHouseholdSubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdGeneralInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HouseholdGeneralInfoDto),
    __metadata("design:type", HouseholdGeneralInfoDto)
], CreateHouseholdSubmissionDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdWaterSupplyDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HouseholdWaterSupplyDto),
    __metadata("design:type", HouseholdWaterSupplyDto)
], CreateHouseholdSubmissionDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdSanitationDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HouseholdSanitationDto),
    __metadata("design:type", HouseholdSanitationDto)
], CreateHouseholdSubmissionDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdHygieneDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HouseholdHygieneDto),
    __metadata("design:type", HouseholdHygieneDto)
], CreateHouseholdSubmissionDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdSolidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HouseholdSolidWasteManagementDto),
    __metadata("design:type", HouseholdSolidWasteManagementDto)
], CreateHouseholdSubmissionDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdLiquidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => HouseholdLiquidWasteManagementDto),
    __metadata("design:type", HouseholdLiquidWasteManagementDto)
], CreateHouseholdSubmissionDto.prototype, "liquidWaste", void 0);
class HouseholdSubmissionResponseDto extends base_submission_dto_1.BaseSubmissionResponseDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.HouseholdSubmissionResponseDto = HouseholdSubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdGeneralInfoDto }),
    __metadata("design:type", HouseholdGeneralInfoDto)
], HouseholdSubmissionResponseDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdWaterSupplyDto }),
    __metadata("design:type", HouseholdWaterSupplyDto)
], HouseholdSubmissionResponseDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdSanitationDto }),
    __metadata("design:type", HouseholdSanitationDto)
], HouseholdSubmissionResponseDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdHygieneDto }),
    __metadata("design:type", HouseholdHygieneDto)
], HouseholdSubmissionResponseDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdSolidWasteManagementDto }),
    __metadata("design:type", HouseholdSolidWasteManagementDto)
], HouseholdSubmissionResponseDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: HouseholdLiquidWasteManagementDto }),
    __metadata("design:type", HouseholdLiquidWasteManagementDto)
], HouseholdSubmissionResponseDto.prototype, "liquidWaste", void 0);
//# sourceMappingURL=household.dto.js.map