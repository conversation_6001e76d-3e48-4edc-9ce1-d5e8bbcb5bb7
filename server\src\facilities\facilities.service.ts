import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateHouseholdDto,
  UpdateHouseholdDto,
  HouseholdResponseDto,
  HouseholdsListResponseDto,
  CreateHouseholdResponseDto,
  UpdateHouseholdResponseDto,
} from './dto/household.dto';
import {
  CreateSchoolDto,
  UpdateSchoolDto,
  SchoolResponseDto,
  SchoolsListResponseDto,
  CreateSchoolResponseDto,
  UpdateSchoolResponseDto,
} from './dto/school.dto';
import {
  CreateHealthFacilityDto,
  UpdateHealthFacilityDto,
  HealthFacilityResponseDto,
  HealthFacilitiesListResponseDto,
  CreateHealthFacilityResponseDto,
  UpdateHealthFacilityResponseDto,
} from './dto/health-facility.dto';
import {
  CreatePublicPlaceDto,
  UpdatePublicPlaceDto,
  PublicPlaceResponseDto,
  PublicPlacesListResponseDto,
  CreatePublicPlaceResponseDto,
  UpdatePublicPlaceResponseDto,
} from './dto/public-place.dto';
import { BaseDeleteResponseDto } from './dto/base-facility.dto';

@Injectable()
export class FacilitiesService {
  constructor(private prisma: PrismaService) { }


  async generateHouseholdNumber(
    villageId: number,
  ): Promise<string> {

    const lastRecord = await this.prisma.houseHold.findFirst({
      where: {
        number: {
          startsWith: `${villageId}`,
        },
      },
      orderBy: {
        number: "desc",
      },
    });

    if (!lastRecord) {
      return `${villageId}0001`;
    }

    const lastNumber = parseInt(lastRecord.number.slice(-4), 10);
    const nextNumber = lastNumber + 1;

    return `${villageId}${String(nextNumber).padStart(4, "0")}`;
  }

  async generateSchoolNumber(
    villageId: number,
  ): Promise<string> {

    const lastRecord = await this.prisma.school.findFirst({
      where: {
        number: {
          startsWith: `${villageId}`,
        },
      },
      orderBy: {
        number: "desc",
      },
    });

    if (!lastRecord) {
      return `${villageId}0001`;
    }

    const lastNumber = parseInt(lastRecord.number.slice(-4), 10);
    const nextNumber = lastNumber + 1;

    return `${villageId}${String(nextNumber).padStart(4, "0")}`;
  }

  async generateHealthFacilityNumber(
    villageId: number,
  ): Promise<string> {

    const lastRecord = await this.prisma.healthFacility.findFirst({
      where: {
        number: {
          startsWith: `${villageId}`,
        },
      },
      orderBy: {
        number: "desc",
      },
    });

    if (!lastRecord) {
      return `${villageId}0001`;
    }

    const lastNumber = parseInt(lastRecord.number.slice(-4), 10);
    const nextNumber = lastNumber + 1;

    return `${villageId}${String(nextNumber).padStart(4, "0")}`;
  }

  async generatePublicPlaceNumber(
    villageId: number,
  ): Promise<string> {

    const lastRecord = await this.prisma.publicPlace.findFirst({
      where: {
        number: {
          startsWith: `${villageId}`,
        },
      },
      orderBy: {
        number: "desc",
      },
    });

    if (!lastRecord) {
      return `${villageId}0001`;
    }

    const lastNumber = parseInt(lastRecord.number.slice(-4), 10);
    const nextNumber = lastNumber + 1;

    return `${villageId}${String(nextNumber).padStart(4, "0")}`;
  }

  // ==================== HOUSEHOLD METHODS ====================

  async createHousehold(createHouseholdDto: CreateHouseholdDto): Promise<CreateHouseholdResponseDto> {

    const locationId = await this.handleLocationForFacility(
      createHouseholdDto.locationId,
      createHouseholdDto.locationData,
    );

    const location = await this.prisma.location.findUnique({
      where: { id: locationId },
    });

    const generatedNumber = await this.generateHouseholdNumber(location.villageId);

    const household = await this.prisma.houseHold.create({
      data: {
        locationId: locationId,
        number: generatedNumber,
        headOfHouseholdName: createHouseholdDto.headOfHouseholdName,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      facility: this.formatHouseholdResponse(household),
      message: 'Household created successfully',
    };
  }

  async findAllHouseholds(
    page: number = 1,
    limit: number = 10,
    villageId: number,
    search?: string,
  ): Promise<HouseholdsListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      deleted: false,
      location: {
        is: { villageId },
      },
      ...(search && {
        OR: [
          { number: { contains: search, mode: 'insensitive' as const } },
          { headOfHouseholdName: { contains: search, mode: 'insensitive' as const } },
        ],
      }),
    };

    const [households, total] = await Promise.all([
      this.prisma.houseHold.findMany({
        where,
        skip,
        take: limit,
        include: {
          location: {
            include: {
              village: {
                include: {
                  cell: {
                    include: {
                      sector: {
                        include: {
                          district: {
                            include: {
                              province: true,
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          number: 'asc',
        },
      }),
      this.prisma.houseHold.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: households.map((household) => this.formatHouseholdResponse(household)),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOneHousehold(id: string): Promise<HouseholdResponseDto> {
    const household = await this.prisma.houseHold.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!household) {
      throw new NotFoundException('Household not found');
    }

    return this.formatHouseholdResponse(household);
  }

  async updateHousehold(
    id: string,
    updateHouseholdDto: UpdateHouseholdDto,
  ): Promise<UpdateHouseholdResponseDto> {
    const existingHousehold = await this.prisma.houseHold.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingHousehold) {
      throw new NotFoundException('Household not found');
    }

    // Check if new number conflicts with existing household
    if (updateHouseholdDto.number && updateHouseholdDto.number !== existingHousehold.number) {
      const conflictingHousehold = await this.prisma.houseHold.findUnique({
        where: { number: updateHouseholdDto.number },
      });

      if (conflictingHousehold) {
        throw new ConflictException('Household number already exists');
      }
    }

    // Handle location update if provided
    let locationIdToUpdate = existingHousehold.locationId;
    if (updateHouseholdDto.locationId || updateHouseholdDto.locationData) {
      locationIdToUpdate = await this.handleLocationForFacility(
        updateHouseholdDto.locationId,
        updateHouseholdDto.locationData,
      );
    }

    const household = await this.prisma.houseHold.update({
      where: { id },
      data: {
        ...updateHouseholdDto,
        locationId: locationIdToUpdate,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      facility: this.formatHouseholdResponse(household),
      message: 'Household updated successfully',
    };
  }

  async removeHousehold(id: string): Promise<BaseDeleteResponseDto> {
    const household = await this.prisma.houseHold.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!household) {
      throw new NotFoundException('Household not found');
    }

    await this.prisma.houseHold.update({
      where: { id },
      data: { deleted: true },
    });

    return {
      message: 'Household deleted successfully',
    };
  }

  // ==================== HELPER METHODS ====================

  private formatHouseholdResponse(household: any): HouseholdResponseDto {
    return {
      id: household.id,
      number: household.number,
      headOfHouseholdName: household.headOfHouseholdName,
      location: {
        id: household.location.id,
        villageId: household.location.villageId,
        village: {
          id: household.location.village.id,
          code: household.location.village.code,
          name: household.location.village.name,
          cell: {
            id: household.location.village.cell.id,
            code: household.location.village.cell.code,
            name: household.location.village.cell.name,
            sector: {
              id: household.location.village.cell.sector.id,
              name: household.location.village.cell.sector.name,
              code: household.location.village.cell.sector.code,
              district: {
                id: household.location.village.cell.sector.district.id,
                code: household.location.village.cell.sector.district.code,
                name: household.location.village.cell.sector.district.name,
                province: {
                  id: household.location.village.cell.sector.district.province.id,
                  code: household.location.village.cell.sector.district.province.code,
                  name: household.location.village.cell.sector.district.province.name,
                },
              },
            },
          },
        },
        latitude: household.location.latitude,
        longitude: household.location.longitude,
        settlementType: household.location.settlementType,
      },
      createdAt: household.createdAt,
      updatedAt: household.updatedAt,
    };
  }

  // ==================== SCHOOL METHODS ====================

  async createSchool(createSchoolDto: CreateSchoolDto): Promise<CreateSchoolResponseDto> {

    const locationId = await this.handleLocationForFacility(
      createSchoolDto.locationId,
      createSchoolDto.locationData,
    );

    const location = await this.prisma.location.findUnique({
      where: { id: locationId },
    });

    const generatedNumber = await this.generateSchoolNumber(location.villageId);

    const school = await this.prisma.school.create({
      data: {
        locationId: locationId,
        number: generatedNumber,
        name: createSchoolDto.name,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      facility: this.formatSchoolResponse(school),
      message: 'School created successfully',
    };
  }

  async findAllSchools(
    page: number = 1,
    limit: number = 10,
    villageId: number,
    search?: string,
  ): Promise<SchoolsListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      deleted: false,
      location: {
        is: { villageId },
      },
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { number: { contains: search, mode: 'insensitive' as const } },
        ].filter(Boolean),
      }),
    };

    const [schools, total] = await Promise.all([
      this.prisma.school.findMany({
        where,
        skip,
        take: limit,
        include: {
          location: {
            include: {
              village: {
                include: {
                  cell: {
                    include: {
                      sector: {
                        include: {
                          district: {
                            include: {
                              province: true,
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          number: 'asc',
        },
      }),
      this.prisma.school.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: schools.map((school) => this.formatSchoolResponse(school)),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOneSchool(id: string): Promise<SchoolResponseDto> {
    const school = await this.prisma.school.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!school) {
      throw new NotFoundException('School not found');
    }

    return this.formatSchoolResponse(school);
  }

  async updateSchool(
    id: string,
    updateSchoolDto: UpdateSchoolDto,
  ): Promise<UpdateSchoolResponseDto> {
    const existingSchool = await this.prisma.school.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingSchool) {
      throw new NotFoundException('School not found');
    }

    // Check if new number conflicts with existing school
    if (updateSchoolDto.number && updateSchoolDto.number !== existingSchool.number) {
      const conflictingSchool = await this.prisma.school.findUnique({
        where: { number: updateSchoolDto.number },
      });

      if (conflictingSchool) {
        throw new ConflictException('School number already exists');
      }
    }

    // Verify location exists if provided
    if (updateSchoolDto.locationId) {
      const location = await this.prisma.location.findUnique({
        where: { id: updateSchoolDto.locationId },
      });

      if (!location) {
        throw new NotFoundException('Location not found');
      }
    }

    const school = await this.prisma.school.update({
      where: { id },
      data: updateSchoolDto,
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      facility: this.formatSchoolResponse(school),
      message: 'School updated successfully',
    };
  }

  async removeSchool(id: string): Promise<BaseDeleteResponseDto> {
    const school = await this.prisma.school.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!school) {
      throw new NotFoundException('School not found');
    }

    await this.prisma.school.update({
      where: { id },
      data: { deleted: true },
    });

    return {
      message: 'School deleted successfully',
    };
  }

  private formatSchoolResponse(school: any): SchoolResponseDto {
    return {
      id: school.id,
      number: school.number,
      name: school.name,
      location: {
        id: school.location.id,
        villageId: school.location.villageId,
        village: {
          id: school.location.village.id,
          code: school.location.village.code,
          name: school.location.village.name,
          cell: {
            id: school.location.village.cell.id,
            code: school.location.village.cell.code,
            name: school.location.village.cell.name,
            sector: {
              id: school.location.village.cell.sector.id,
              code: school.location.village.cell.sector.code,
              name: school.location.village.cell.sector.name,
              district: {
                id: school.location.village.cell.sector.district.id,
                code: school.location.village.cell.sector.district.code,
                name: school.location.village.cell.sector.district.name,
                province: {
                  id: school.location.village.cell.sector.district.province.id,
                  code: school.location.village.cell.sector.district.province.code,
                  name: school.location.village.cell.sector.district.province.name,
                },
              },
            },
          },
        },
        latitude: school.location.latitude,
        longitude: school.location.longitude,
        settlementType: school.location.settlementType,
      },
      createdAt: school.createdAt,
      updatedAt: school.updatedAt,
    };
  }

  // ==================== HEALTH FACILITY METHODS ====================

  async createHealthFacility(createHealthFacilityDto: CreateHealthFacilityDto): Promise<CreateHealthFacilityResponseDto> {
    const locationId = await this.handleLocationForFacility(
      createHealthFacilityDto.locationId,
      createHealthFacilityDto.locationData,
    );

    const location = await this.prisma.location.findUnique({
      where: { id: locationId },
    });

    const generatedNumber = await this.generateHealthFacilityNumber(location.villageId);

    const healthFacility = await this.prisma.healthFacility.create({
      data: {
        locationId: locationId,
        number: generatedNumber,
        name: createHealthFacilityDto.name,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      facility: this.formatHealthFacilityResponse(healthFacility),
      message: 'Health facility created successfully',
    };
  }

  async findAllHealthFacilities(
    page: number = 1,
    limit: number = 10,
    villageId: number,
    search?: string,
  ): Promise<HealthFacilitiesListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      deleted: false,
      location: {
        is: { villageId },
      },
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { number: { contains: search, mode: 'insensitive' as const } },
        ].filter(Boolean),
      }),
    };

    const [healthFacilities, total] = await Promise.all([
      this.prisma.healthFacility.findMany({
        where,
        skip,
        take: limit,
        include: {
          location: {
            include: {
              village: {
                include: {
                  cell: {
                    include: {
                      sector: {
                        include: {
                          district: {
                            include: {
                              province: true,
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          number: 'asc',
        },
      }),
      this.prisma.healthFacility.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: healthFacilities.map((healthFacility) => this.formatHealthFacilityResponse(healthFacility)),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOneHealthFacility(id: string): Promise<HealthFacilityResponseDto> {
    const healthFacility = await this.prisma.healthFacility.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!healthFacility) {
      throw new NotFoundException('Health facility not found');
    }

    return this.formatHealthFacilityResponse(healthFacility);
  }

  async updateHealthFacility(
    id: string,
    updateHealthFacilityDto: UpdateHealthFacilityDto,
  ): Promise<UpdateHealthFacilityResponseDto> {
    const existingHealthFacility = await this.prisma.healthFacility.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingHealthFacility) {
      throw new NotFoundException('Health facility not found');
    }

    // Check if new number conflicts with existing health facility
    if (updateHealthFacilityDto.number && updateHealthFacilityDto.number !== existingHealthFacility.number) {
      const conflictingHealthFacility = await this.prisma.healthFacility.findUnique({
        where: { number: updateHealthFacilityDto.number },
      });

      if (conflictingHealthFacility) {
        throw new ConflictException('Health facility number already exists');
      }
    }

    // Verify location exists if provided
    if (updateHealthFacilityDto.locationId) {
      const location = await this.prisma.location.findUnique({
        where: { id: updateHealthFacilityDto.locationId },
      });

      if (!location) {
        throw new NotFoundException('Location not found');
      }
    }

    const healthFacility = await this.prisma.healthFacility.update({
      where: { id },
      data: updateHealthFacilityDto,
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      facility: this.formatHealthFacilityResponse(healthFacility),
      message: 'Health facility updated successfully',
    };
  }

  async removeHealthFacility(id: string): Promise<BaseDeleteResponseDto> {
    const healthFacility = await this.prisma.healthFacility.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!healthFacility) {
      throw new NotFoundException('Health facility not found');
    }

    await this.prisma.healthFacility.update({
      where: { id },
      data: { deleted: true },
    });

    return {
      message: 'Health facility deleted successfully',
    };
  }

  private formatHealthFacilityResponse(healthFacility: any): HealthFacilityResponseDto {
    return {
      id: healthFacility.id,
      number: healthFacility.number,
      name: healthFacility.name,
      location: {
        id: healthFacility.location.id,
        villageId: healthFacility.location.villageId,
        village: {
          id: healthFacility.location.village.id,
          code: healthFacility.location.village.code,
          name: healthFacility.location.village.name,
          cell: {
            id: healthFacility.location.village.cell.id,
            code: healthFacility.location.village.cell.code,
            name: healthFacility.location.village.cell.name,
            sector: {
              id: healthFacility.location.village.cell.sector.id,
              code: healthFacility.location.village.cell.sector.code,
              name: healthFacility.location.village.cell.sector.name,
              district: {
                id: healthFacility.location.village.cell.sector.district.id,
                code: healthFacility.location.village.cell.sector.district.code,
                name: healthFacility.location.village.cell.sector.district.name,
                province: {
                  id: healthFacility.location.village.cell.sector.district.province.id,
                  code: healthFacility.location.village.cell.sector.district.province.code,
                  name: healthFacility.location.village.cell.sector.district.province.name,
                },
              },
            },
          },
        },
        latitude: healthFacility.location.latitude,
        longitude: healthFacility.location.longitude,
        settlementType: healthFacility.location.settlementType,
      },
      createdAt: healthFacility.createdAt,
      updatedAt: healthFacility.updatedAt,
    };
  }

  // ==================== PUBLIC PLACE METHODS ====================

  async createPublicPlace(createPublicPlaceDto: CreatePublicPlaceDto): Promise<CreatePublicPlaceResponseDto> {
    const locationId = await this.handleLocationForFacility(
      createPublicPlaceDto.locationId,
      createPublicPlaceDto.locationData,
    );

    const location = await this.prisma.location.findUnique({
      where: { id: locationId },
    });

    const generatedNumber = await this.generatePublicPlaceNumber(location.villageId);

    const publicPlace = await this.prisma.publicPlace.create({
      data: {
        locationId: locationId,
        number: generatedNumber,
        name: createPublicPlaceDto.name,
        type: createPublicPlaceDto.type,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      facility: this.formatPublicPlaceResponse(publicPlace),
      message: 'Public place created successfully',
    };
  }

  async findAllPublicPlaces(
    page: number = 1,
    limit: number = 10,
    villageId: number,
    search?: string,
  ): Promise<PublicPlacesListResponseDto> {
    const skip = (page - 1) * limit;

    const where = {
      deleted: false,
      location: {
        is: { villageId },
      },
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { number: { contains: search, mode: 'insensitive' as const } },
        ].filter(Boolean),
      }),
    };

    const [publicPlaces, total] = await Promise.all([
      this.prisma.publicPlace.findMany({
        where,
        skip,
        take: limit,
        include: {
          location: {
            include: {
              village: {
                include: {
                  cell: {
                    include: {
                      sector: {
                        include: {
                          district: {
                            include: {
                              province: true,
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          number: 'asc',
        },
      }),
      this.prisma.publicPlace.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: publicPlaces.map((publicPlace) => this.formatPublicPlaceResponse(publicPlace)),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOnePublicPlace(id: string): Promise<PublicPlaceResponseDto> {
    const publicPlace = await this.prisma.publicPlace.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!publicPlace) {
      throw new NotFoundException('Public place not found');
    }

    return this.formatPublicPlaceResponse(publicPlace);
  }

  async updatePublicPlace(
    id: string,
    updatePublicPlaceDto: UpdatePublicPlaceDto,
  ): Promise<UpdatePublicPlaceResponseDto> {
    const existingPublicPlace = await this.prisma.publicPlace.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingPublicPlace) {
      throw new NotFoundException('Public place not found');
    }

    // Check if new number conflicts with existing public place
    if (updatePublicPlaceDto.number && updatePublicPlaceDto.number !== existingPublicPlace.number) {
      const conflictingPublicPlace = await this.prisma.publicPlace.findUnique({
        where: { number: updatePublicPlaceDto.number },
      });

      if (conflictingPublicPlace) {
        throw new ConflictException('Public place number already exists');
      }
    }

    // Handle location update if provided
    let locationIdToUpdate = existingPublicPlace.locationId;
    if (updatePublicPlaceDto.locationId || updatePublicPlaceDto.locationData) {
      locationIdToUpdate = await this.handleLocationForFacility(
        updatePublicPlaceDto.locationId,
        updatePublicPlaceDto.locationData,
      );
    }

    const publicPlace = await this.prisma.publicPlace.update({
      where: { id },
      data: {
        ...updatePublicPlaceDto,
        locationId: locationIdToUpdate,
      },
      include: {
        location: {
          include: {
            village: {
              include: {
                cell: {
                  include: {
                    sector: {
                      include: {
                        district: {
                          include: {
                            province: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      facility: this.formatPublicPlaceResponse(publicPlace),
      message: 'Public place updated successfully',
    };
  }

  async removePublicPlace(id: string): Promise<BaseDeleteResponseDto> {
    const publicPlace = await this.prisma.publicPlace.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!publicPlace) {
      throw new NotFoundException('Public place not found');
    }

    await this.prisma.publicPlace.update({
      where: { id },
      data: { deleted: true },
    });

    return {
      message: 'Public place deleted successfully',
    };
  }

  private formatPublicPlaceResponse(publicPlace: any): PublicPlaceResponseDto {
    return {
      id: publicPlace.id,
      number: publicPlace.number,
      name: publicPlace.name,
      type: publicPlace.type,
      location: {
        id: publicPlace.location.id,
        villageId: publicPlace.location.villageId,
        village: {
          id: publicPlace.location.village.id,
          code: publicPlace.location.village.code,
          name: publicPlace.location.village.name,
          cell: {
            id: publicPlace.location.village.cell.id,
            code: publicPlace.location.village.cell.code,
            name: publicPlace.location.village.cell.name,
            sector: {
              id: publicPlace.location.village.cell.sector.id,
              code: publicPlace.location.village.cell.sector.code,
              name: publicPlace.location.village.cell.sector.name,
              district: {
                id: publicPlace.location.village.cell.sector.district.id,
                code: publicPlace.location.village.cell.sector.district.code,
                name: publicPlace.location.village.cell.sector.district.name,
                province: {
                  id: publicPlace.location.village.cell.sector.district.province.id,
                  code: publicPlace.location.village.cell.sector.district.province.code,
                  name: publicPlace.location.village.cell.sector.district.province.name,
                },
              },
            },
          },
        },
        latitude: publicPlace.location.latitude,
        longitude: publicPlace.location.longitude,
        settlementType: publicPlace.location.settlementType,
      },
      createdAt: publicPlace.createdAt,
      updatedAt: publicPlace.updatedAt,
    };
  }

  // ==================== HELPER METHODS ====================

  private async handleLocationForFacility(
    locationId?: string,
    locationData?: any,
  ): Promise<string> {
    // Validate that exactly one of locationId or locationData is provided
    if (!locationId && !locationData) {
      throw new BadRequestException('Either locationId or locationData must be provided');
    }

    if (locationId && locationData) {
      throw new BadRequestException('Cannot provide both locationId and locationData');
    }

    if (locationId) {
      // Verify existing location exists
      const location = await this.prisma.location.findUnique({
        where: { id: locationId },
      });

      if (!location) {
        throw new NotFoundException('Location not found');
      }

      return locationId;
    }

    if (locationData) {
      // Verify village exists
      const village = await this.prisma.village.findUnique({
        where: { id: locationData.villageId },
      });

      if (!village) {
        throw new NotFoundException('Village not found');
      }

      // Create new location
      const newLocation = await this.prisma.location.create({
        data: {
          villageId: locationData.villageId,
          latitude: locationData.latitude,
          longitude: locationData.longitude,
          settlementType: locationData.settlementType || 'RURAL',
        },
      });

      return newLocation.id;
    }

    throw new BadRequestException('Invalid location data');
  }
}
