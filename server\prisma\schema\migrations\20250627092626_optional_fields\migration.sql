/*
  Warnings:

  - You are about to drop the `Account` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Cell` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `District` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HealthFacility` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HealthFacilityGeneralInfo` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HealthFacilityHygiene` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HealthFacilityLiquidWasteManagement` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HealthFacilitySanitation` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HealthFacilitySolidWasteManagement` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HealthFacilityWaterSupply` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HouseHold` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HouseHoldGeneralInfo` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HouseHoldHygiene` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HouseHoldLiquidWasteManagement` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HouseHoldSanitation` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HouseHoldSolidWasteManagement` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HouseHoldWaterSupply` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Location` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Market` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MarketGeneralInfo` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MarketHygiene` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MarketLiquidWasteManagement` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MarketSanitation` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MarketSolidWasteManagement` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MarketWaterSupply` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Province` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `RecoveryCode` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Role` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `School` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SchoolGeneralInfo` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SchoolHygiene` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SchoolLiquidWasteManagement` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SchoolSanitation` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SchoolSolidWasteManagement` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SchoolWaterSupply` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Sector` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Submission` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `User` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserLocationAccess` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Village` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `WasteCollectionCompany` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `WasteDisposalCompany` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `WasteRecoveryCompany` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "SettlementType" AS ENUM ('RURAL', 'URBAN');

-- CreateEnum
CREATE TYPE "EducationLevel" AS ENUM ('none', 'primary', 'O level', 'A level', 'tertiary');

-- CreateEnum
CREATE TYPE "Gender" AS ENUM ('MALE', 'FEMALE', 'OTHER');

-- CreateEnum
CREATE TYPE "AgeGroup" AS ENUM ('below 18', 'between 18 and 30', 'between 31 and 50', 'above 50');

-- CreateEnum
CREATE TYPE "MainWaterSource" AS ENUM ('water tap within the HH/Facility', 'Public water tap/kiosk', 'Protected/improved springs', 'Borehole', 'Rain water', 'Unimproved springs', 'Surface water (Lake, rivers, etc.)', 'From neighbor');

-- CreateEnum
CREATE TYPE "WaterAvailability" AS ENUM ('HH members can find water whenever needed', 'Sometimes HH members cannot find water to use');

-- CreateEnum
CREATE TYPE "WaterAvailabilityFrequency" AS ENUM ('Once per week', 'Twice a week', '3 days a week', 'Once a month', 'Once in a period more than a month', 'none of the above');

-- CreateEnum
CREATE TYPE "CleanWaterStorageCapacity" AS ENUM ('1 - 500 liters', '501 liters - 2 m3', '2m3 - 5m3', '5m3 - 10m3', 'Above 10m3');

-- CreateEnum
CREATE TYPE "WaterSourceDistance" AS ENUM ('1 m - 200 m', '201 m- 500 m', '501 m - 1 km', 'Above 1 km');

-- CreateEnum
CREATE TYPE "WaterFetchingTime" AS ENUM ('1 min - 30 min', '31 min - 1hour min', 'Above one hour');

-- CreateEnum
CREATE TYPE "UnimprovedWaterReason" AS ENUM ('improved water source is far', 'The nearby public water source does not function', 'Other');

-- CreateEnum
CREATE TYPE "PwsNonFunctionalityReason" AS ENUM ('PWS did never function at all', 'Water vanished from the PWS', 'Damaged');

-- CreateEnum
CREATE TYPE "PwsNonFunctionalityDuration" AS ENUM ('Between 1 week and a month', '1 month - 3 months', 'Above 3 months');

-- CreateEnum
CREATE TYPE "ToiletFacilityType" AS ENUM ('Flush to piped sewer system', 'Flush to septic tank', 'Flush to pit', 'Ventilated improved pit latrine', 'Pit latrine with slab', 'Pit latrine without slab', 'Composting toilet (Ecosan)', 'Urine diversion dry toilet', 'No facility/bush/field', 'Other (specify):');

-- CreateEnum
CREATE TYPE "ToiletFacilityCompleteness" AS ENUM ('Has roof and closing door', 'Has roof but without closing door', 'Has closing door, but no roof', 'Has not complete walls', 'Has no walls');

-- CreateEnum
CREATE TYPE "FacilitySlabConstructionMaterial" AS ENUM ('Dry trees or unordered wood ', 'Wood well ordered (without holes other than the drop hole)', 'Solid land with holes (besides the drop hole)', 'Solid land floor without holes', 'Concrete with cement floor', 'Concrete with tiles floor');

-- CreateEnum
CREATE TYPE "ExcretaManagement" AS ENUM ('Excreta was emptied and transported by a specialized company', 'The toilet was filled with land, ashes, etc. and was left', 'The toilet was left without filling it with land, ashes, etc.', 'The toilet was filled with land, ashes, etc. then composted', 'The toilet was directed (discharged) into another pit');

-- CreateEnum
CREATE TYPE "HandWashingFacilityType" AS ENUM ('Mobile facility', 'Fixed facility');

-- CreateEnum
CREATE TYPE "HandWashingMaterial" AS ENUM ('Water and soap', 'Only water', 'None of them');

-- CreateEnum
CREATE TYPE "MarketHandWashingMaterial" AS ENUM ('Water, soap and sanitizer', 'Water and soap', 'Sanitizer', 'Only water', 'Neither water nor soap');

-- CreateEnum
CREATE TYPE "WasteWaterManagement" AS ENUM ('Channeled to a dedicated pit which is covered', 'Channeled to a dedicated pit which is not covered', 'Channeled to septic tank', 'Channeled to sewers', 'Flows in open trenches/drains', 'Flows on ground', 'They get recycled');

-- CreateEnum
CREATE TYPE "WasteManagementAfterSeparation" AS ENUM ('Transported by waste collection companies', 'on site treatment', 'Both (a) and (b)');

-- CreateEnum
CREATE TYPE "WasteTreatmentType" AS ENUM ('Composting (fertilizer)', 'Buried under ground.', 'Burnt.', 'Sort materials and sell to specialized companies', 'Autoclaving', 'Incineration', 'Microwave treatment', 'Chemical disinfection');

-- CreateEnum
CREATE TYPE "WasteCollectionFrequency" AS ENUM ('Daily', '2 times a week', '3 times a week', 'Weekly', 'Other: (please specify)');

-- CreateEnum
CREATE TYPE "SchoolType" AS ENUM ('Early Childhood Development (ECD)', 'Pre-primary', 'Primary', 'Secondary', 'Combined pre-primary & primary', 'Combined primary & secondary', 'Combined pre-primary, primary & secondary', 'higher education');

-- CreateEnum
CREATE TYPE "SchoolManagement" AS ENUM ('Public School', 'Private school', 'Faith-based', 'Faith-based co-financed by Government', 'Private co-financed by Government');

-- CreateEnum
CREATE TYPE "SchoolTypeDayBoarding" AS ENUM ('Day school', 'Boarding school', 'Day and boarding school');

-- CreateEnum
CREATE TYPE "HealthFacilityType" AS ENUM ('Referral hospital', 'Provincial hospital', 'District hospital', 'Health Center', 'health post', 'Polyclinic', 'Clinic', 'Dispensary', 'Others');

-- CreateEnum
CREATE TYPE "HealthFacilityManagement" AS ENUM ('Public', 'Private', 'Faith-based', 'Private HF supported by Govt', 'Faith-based HF supported by Govt');

-- CreateEnum
CREATE TYPE "DailyPatientVolume" AS ENUM ('1-50', '51-100', '100-300', 'above 300');

-- CreateEnum
CREATE TYPE "MarketCategory" AS ENUM ('fully constructed', 'partially constructed', 'Not constructed');

-- CreateEnum
CREATE TYPE "MarketOpeningDays" AS ENUM ('Once a week', 'Twice a week', 'three days a week', 'Every day');

-- CreateEnum
CREATE TYPE "ServiceProviderType" AS ENUM ('municipal/public operator', 'Private service contractor', 'NGO/CSOs', 'Other');

-- CreateEnum
CREATE TYPE "ClientType" AS ENUM ('Households', 'Markets', 'Schools', 'Health facilities', 'Industries/Factories', 'Businesses/restaurants/ Hotels/(street) vendors', 'Administrative buildings', 'Others');

-- CreateEnum
CREATE TYPE "WasteMaterial" AS ENUM ('Biodegradable', 'Non-biodegradable');

-- CreateEnum
CREATE TYPE "WasteDestination" AS ENUM ('Dumpsite/landfill', 'Material recovery/recycling facility', 'Others');

-- CreateEnum
CREATE TYPE "RecordingMethod" AS ENUM ('paper logbook', 'others');

-- CreateEnum
CREATE TYPE "OperationType" AS ENUM ('End-of-chain recycler/recoverer facility ', 'Apex trader (Buying and Selling recycled commodities)', 'Intermediate trader person or company');

-- CreateEnum
CREATE TYPE "CompactionFrequency" AS ENUM ('Daily', 'Weekly', 'Monthly', 'Annually', 'Emergency only', 'Never');

-- CreateEnum
CREATE TYPE "TruckFrequency" AS ENUM ('Daily', 'Weekly', 'Other');

-- CreateEnum
CREATE TYPE "FacilityType" AS ENUM ('HOUSEHOLD', 'SCHOOL', 'HEALTH_FACILITY', 'MARKET', 'WASTE_COLLECTION_COMPANY', 'WASTE_RECOVERY_COMPANY', 'WASTE_DISPOSAL_COMPANY');

-- CreateEnum
CREATE TYPE "Privilege" AS ENUM ('USER_MANAGEMENT', 'DATA_COLLECTION');

-- DropForeignKey
ALTER TABLE "public"."Account" DROP CONSTRAINT "Account_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Cell" DROP CONSTRAINT "Cell_sectorId_fkey";

-- DropForeignKey
ALTER TABLE "public"."District" DROP CONSTRAINT "District_provinceId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HealthFacility" DROP CONSTRAINT "HealthFacility_locationId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HealthFacilityGeneralInfo" DROP CONSTRAINT "HealthFacilityGeneralInfo_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HealthFacilityHygiene" DROP CONSTRAINT "HealthFacilityHygiene_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HealthFacilityLiquidWasteManagement" DROP CONSTRAINT "HealthFacilityLiquidWasteManagement_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HealthFacilitySanitation" DROP CONSTRAINT "HealthFacilitySanitation_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HealthFacilitySolidWasteManagement" DROP CONSTRAINT "HealthFacilitySolidWasteManagement_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HealthFacilityWaterSupply" DROP CONSTRAINT "HealthFacilityWaterSupply_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HouseHold" DROP CONSTRAINT "HouseHold_locationId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HouseHoldGeneralInfo" DROP CONSTRAINT "HouseHoldGeneralInfo_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HouseHoldHygiene" DROP CONSTRAINT "HouseHoldHygiene_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HouseHoldLiquidWasteManagement" DROP CONSTRAINT "HouseHoldLiquidWasteManagement_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HouseHoldSanitation" DROP CONSTRAINT "HouseHoldSanitation_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HouseHoldSolidWasteManagement" DROP CONSTRAINT "HouseHoldSolidWasteManagement_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."HouseHoldWaterSupply" DROP CONSTRAINT "HouseHoldWaterSupply_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Location" DROP CONSTRAINT "Location_villageId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Market" DROP CONSTRAINT "Market_locationId_fkey";

-- DropForeignKey
ALTER TABLE "public"."MarketGeneralInfo" DROP CONSTRAINT "MarketGeneralInfo_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."MarketHygiene" DROP CONSTRAINT "MarketHygiene_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."MarketLiquidWasteManagement" DROP CONSTRAINT "MarketLiquidWasteManagement_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."MarketSanitation" DROP CONSTRAINT "MarketSanitation_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."MarketSolidWasteManagement" DROP CONSTRAINT "MarketSolidWasteManagement_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."MarketWaterSupply" DROP CONSTRAINT "MarketWaterSupply_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."RecoveryCode" DROP CONSTRAINT "RecoveryCode_accountId_fkey";

-- DropForeignKey
ALTER TABLE "public"."School" DROP CONSTRAINT "School_locationId_fkey";

-- DropForeignKey
ALTER TABLE "public"."SchoolGeneralInfo" DROP CONSTRAINT "SchoolGeneralInfo_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."SchoolHygiene" DROP CONSTRAINT "SchoolHygiene_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."SchoolLiquidWasteManagement" DROP CONSTRAINT "SchoolLiquidWasteManagement_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."SchoolSanitation" DROP CONSTRAINT "SchoolSanitation_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."SchoolSolidWasteManagement" DROP CONSTRAINT "SchoolSolidWasteManagement_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."SchoolWaterSupply" DROP CONSTRAINT "SchoolWaterSupply_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Sector" DROP CONSTRAINT "Sector_districtId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Submission" DROP CONSTRAINT "Submission_healthFacilityId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Submission" DROP CONSTRAINT "Submission_houseHoldId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Submission" DROP CONSTRAINT "Submission_marketId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Submission" DROP CONSTRAINT "Submission_schoolId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Submission" DROP CONSTRAINT "Submission_submittedById_fkey";

-- DropForeignKey
ALTER TABLE "public"."User" DROP CONSTRAINT "User_roleId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserLocationAccess" DROP CONSTRAINT "UserLocationAccess_cellId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserLocationAccess" DROP CONSTRAINT "UserLocationAccess_districtId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserLocationAccess" DROP CONSTRAINT "UserLocationAccess_provinceId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserLocationAccess" DROP CONSTRAINT "UserLocationAccess_sectorId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserLocationAccess" DROP CONSTRAINT "UserLocationAccess_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserLocationAccess" DROP CONSTRAINT "UserLocationAccess_villageId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Village" DROP CONSTRAINT "Village_cellId_fkey";

-- DropForeignKey
ALTER TABLE "public"."WasteCollectionCompany" DROP CONSTRAINT "WasteCollectionCompany_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."WasteDisposalCompany" DROP CONSTRAINT "WasteDisposalCompany_submissionId_fkey";

-- DropForeignKey
ALTER TABLE "public"."WasteRecoveryCompany" DROP CONSTRAINT "WasteRecoveryCompany_submissionId_fkey";

-- DropTable
DROP TABLE "public"."Account";

-- DropTable
DROP TABLE "public"."Cell";

-- DropTable
DROP TABLE "public"."District";

-- DropTable
DROP TABLE "public"."HealthFacility";

-- DropTable
DROP TABLE "public"."HealthFacilityGeneralInfo";

-- DropTable
DROP TABLE "public"."HealthFacilityHygiene";

-- DropTable
DROP TABLE "public"."HealthFacilityLiquidWasteManagement";

-- DropTable
DROP TABLE "public"."HealthFacilitySanitation";

-- DropTable
DROP TABLE "public"."HealthFacilitySolidWasteManagement";

-- DropTable
DROP TABLE "public"."HealthFacilityWaterSupply";

-- DropTable
DROP TABLE "public"."HouseHold";

-- DropTable
DROP TABLE "public"."HouseHoldGeneralInfo";

-- DropTable
DROP TABLE "public"."HouseHoldHygiene";

-- DropTable
DROP TABLE "public"."HouseHoldLiquidWasteManagement";

-- DropTable
DROP TABLE "public"."HouseHoldSanitation";

-- DropTable
DROP TABLE "public"."HouseHoldSolidWasteManagement";

-- DropTable
DROP TABLE "public"."HouseHoldWaterSupply";

-- DropTable
DROP TABLE "public"."Location";

-- DropTable
DROP TABLE "public"."Market";

-- DropTable
DROP TABLE "public"."MarketGeneralInfo";

-- DropTable
DROP TABLE "public"."MarketHygiene";

-- DropTable
DROP TABLE "public"."MarketLiquidWasteManagement";

-- DropTable
DROP TABLE "public"."MarketSanitation";

-- DropTable
DROP TABLE "public"."MarketSolidWasteManagement";

-- DropTable
DROP TABLE "public"."MarketWaterSupply";

-- DropTable
DROP TABLE "public"."Province";

-- DropTable
DROP TABLE "public"."RecoveryCode";

-- DropTable
DROP TABLE "public"."Role";

-- DropTable
DROP TABLE "public"."School";

-- DropTable
DROP TABLE "public"."SchoolGeneralInfo";

-- DropTable
DROP TABLE "public"."SchoolHygiene";

-- DropTable
DROP TABLE "public"."SchoolLiquidWasteManagement";

-- DropTable
DROP TABLE "public"."SchoolSanitation";

-- DropTable
DROP TABLE "public"."SchoolSolidWasteManagement";

-- DropTable
DROP TABLE "public"."SchoolWaterSupply";

-- DropTable
DROP TABLE "public"."Sector";

-- DropTable
DROP TABLE "public"."Submission";

-- DropTable
DROP TABLE "public"."User";

-- DropTable
DROP TABLE "public"."UserLocationAccess";

-- DropTable
DROP TABLE "public"."Village";

-- DropTable
DROP TABLE "public"."WasteCollectionCompany";

-- DropTable
DROP TABLE "public"."WasteDisposalCompany";

-- DropTable
DROP TABLE "public"."WasteRecoveryCompany";

-- DropEnum
DROP TYPE "public"."AgeGroup";

-- DropEnum
DROP TYPE "public"."CleanWaterStorageCapacity";

-- DropEnum
DROP TYPE "public"."ClientType";

-- DropEnum
DROP TYPE "public"."CompactionFrequency";

-- DropEnum
DROP TYPE "public"."DailyPatientVolume";

-- DropEnum
DROP TYPE "public"."EducationLevel";

-- DropEnum
DROP TYPE "public"."ExcretaManagement";

-- DropEnum
DROP TYPE "public"."FacilitySlabConstructionMaterial";

-- DropEnum
DROP TYPE "public"."FacilityType";

-- DropEnum
DROP TYPE "public"."Gender";

-- DropEnum
DROP TYPE "public"."HandWashingFacilityType";

-- DropEnum
DROP TYPE "public"."HandWashingMaterial";

-- DropEnum
DROP TYPE "public"."HealthFacilityManagement";

-- DropEnum
DROP TYPE "public"."HealthFacilityType";

-- DropEnum
DROP TYPE "public"."MainWaterSource";

-- DropEnum
DROP TYPE "public"."MarketCategory";

-- DropEnum
DROP TYPE "public"."MarketHandWashingMaterial";

-- DropEnum
DROP TYPE "public"."MarketOpeningDays";

-- DropEnum
DROP TYPE "public"."OperationType";

-- DropEnum
DROP TYPE "public"."Privilege";

-- DropEnum
DROP TYPE "public"."PwsNonFunctionalityDuration";

-- DropEnum
DROP TYPE "public"."PwsNonFunctionalityReason";

-- DropEnum
DROP TYPE "public"."RecordingMethod";

-- DropEnum
DROP TYPE "public"."SchoolManagement";

-- DropEnum
DROP TYPE "public"."SchoolType";

-- DropEnum
DROP TYPE "public"."SchoolTypeDayBoarding";

-- DropEnum
DROP TYPE "public"."ServiceProviderType";

-- DropEnum
DROP TYPE "public"."SettlementType";

-- DropEnum
DROP TYPE "public"."ToiletFacilityCompleteness";

-- DropEnum
DROP TYPE "public"."ToiletFacilityType";

-- DropEnum
DROP TYPE "public"."TruckFrequency";

-- DropEnum
DROP TYPE "public"."UnimprovedWaterReason";

-- DropEnum
DROP TYPE "public"."WasteCollectionFrequency";

-- DropEnum
DROP TYPE "public"."WasteDestination";

-- DropEnum
DROP TYPE "public"."WasteManagementAfterSeparation";

-- DropEnum
DROP TYPE "public"."WasteMaterial";

-- DropEnum
DROP TYPE "public"."WasteTreatmentType";

-- DropEnum
DROP TYPE "public"."WasteWaterManagement";

-- DropEnum
DROP TYPE "public"."WaterAvailability";

-- DropEnum
DROP TYPE "public"."WaterAvailabilityFrequency";

-- DropEnum
DROP TYPE "public"."WaterFetchingTime";

-- DropEnum
DROP TYPE "public"."WaterSourceDistance";

-- CreateTable
CREATE TABLE "Province" (
    "id" SERIAL NOT NULL,
    "code" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Province_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "District" (
    "id" SERIAL NOT NULL,
    "code" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "provinceId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "District_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Sector" (
    "id" SERIAL NOT NULL,
    "code" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "districtId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Sector_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Cell" (
    "id" SERIAL NOT NULL,
    "code" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "sectorId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Cell_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Village" (
    "id" SERIAL NOT NULL,
    "code" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "cellId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Village_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HealthFacility" (
    "id" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "number" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "HealthFacility_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HealthFacilityGeneralInfo" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "facilityName" TEXT NOT NULL,
    "facilityType" "HealthFacilityType" NOT NULL,
    "managementType" "HealthFacilityManagement" NOT NULL,
    "dailyPatientVolume" "DailyPatientVolume" NOT NULL,
    "totalStaff" INTEGER NOT NULL,

    CONSTRAINT "HealthFacilityGeneralInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HealthFacilityWaterSupply" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "connectedToPipeline" BOOLEAN NOT NULL,
    "waterAvailability" "WaterAvailability" NOT NULL,
    "availableDays" "WaterAvailabilityFrequency",
    "storageCapacity" "CleanWaterStorageCapacity" NOT NULL,
    "mainWaterSource" "MainWaterSource",
    "distanceToSource" "WaterSourceDistance" NOT NULL,

    CONSTRAINT "HealthFacilityWaterSupply_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HealthFacilitySanitation" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "toiletType" "ToiletFacilityType" NOT NULL,
    "slabConstructionMaterial" "FacilitySlabConstructionMaterial" NOT NULL,
    "totalToilets" INTEGER NOT NULL,
    "genderSeparation" BOOLEAN NOT NULL,
    "femaleToilets" INTEGER NOT NULL,
    "maleToilets" INTEGER NOT NULL,
    "disabilityAccess" BOOLEAN NOT NULL,
    "staffToilets" BOOLEAN NOT NULL,
    "hasToiletFullInLast2Years" BOOLEAN NOT NULL,
    "excretaManagement" "ExcretaManagement",

    CONSTRAINT "HealthFacilitySanitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HealthFacilityHygiene" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "handwashingFacility" BOOLEAN NOT NULL,
    "facilityType" "HandWashingFacilityType",
    "handwashingMaterials" "HandWashingMaterial",
    "handWashingfacilityNearToilet" BOOLEAN,
    "toiletHandWashingFacilityType" "HandWashingFacilityType",
    "toiletHandwashingMaterials" "HandWashingMaterial",

    CONSTRAINT "HealthFacilityHygiene_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HealthFacilitySolidWasteManagement" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "wasteSeparation" BOOLEAN NOT NULL,
    "wasteManagement" "WasteManagementAfterSeparation",
    "treatmentType" "WasteTreatmentType",
    "collectionFrequency" "WasteCollectionFrequency",
    "collectionCost" INTEGER,

    CONSTRAINT "HealthFacilitySolidWasteManagement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HealthFacilityLiquidWasteManagement" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "liquidWasteManagement" "WasteWaterManagement" NOT NULL,

    CONSTRAINT "HealthFacilityLiquidWasteManagement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HouseHold" (
    "id" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "number" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "HouseHold_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HouseHoldGeneralInfo" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "headOfHouseholdIdNumber" TEXT NOT NULL,
    "genderOfHead" "Gender" NOT NULL DEFAULT 'MALE',
    "dateOfBirthOfHead" TIMESTAMP(3),
    "educationLevelOfHead" "EducationLevel" NOT NULL,
    "householdSize" INTEGER NOT NULL,
    "childrenUnder18" INTEGER NOT NULL,
    "personsWithDisabilities" INTEGER NOT NULL,

    CONSTRAINT "HouseHoldGeneralInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HouseHoldWaterSupply" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "waterSource" "MainWaterSource" NOT NULL,
    "waterAvailability" "WaterAvailability",
    "availableDays" "WaterAvailabilityFrequency",
    "averageWaterCost" INTEGER,
    "storageCapacity" "CleanWaterStorageCapacity" NOT NULL,
    "distanceToSource" "WaterSourceDistance",
    "timeToFetch" "WaterFetchingTime",
    "jerryCanPrice" INTEGER,
    "unimprovedReason" "UnimprovedWaterReason",
    "pwsNonFunctionalityReason" "PwsNonFunctionalityReason",
    "pwsNonFunctionalityDuration" "PwsNonFunctionalityDuration",

    CONSTRAINT "HouseHoldWaterSupply_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HouseHoldSanitation" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "toiletType" "ToiletFacilityType" NOT NULL,
    "toiletCompleteness" "ToiletFacilityCompleteness",
    "slabConstructionMaterial" "FacilitySlabConstructionMaterial",
    "hasToiletFullInLast2Years" BOOLEAN,
    "toiletShared" BOOLEAN,
    "excretaManagement" "ExcretaManagement",

    CONSTRAINT "HouseHoldSanitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HouseHoldHygiene" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "handwashingFacility" BOOLEAN NOT NULL,
    "handWashingFacilityType" "HandWashingFacilityType",
    "handwashingMaterials" "HandWashingMaterial",

    CONSTRAINT "HouseHoldHygiene_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HouseHoldSolidWasteManagement" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "wasteSeparation" BOOLEAN NOT NULL,
    "wasteManagement" "WasteManagementAfterSeparation",
    "treatmentType" "WasteTreatmentType",
    "collectionFrequency" "WasteCollectionFrequency",

    CONSTRAINT "HouseHoldSolidWasteManagement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HouseHoldLiquidWasteManagement" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "wasterWaterManagement" "WasteWaterManagement" NOT NULL,

    CONSTRAINT "HouseHoldLiquidWasteManagement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Market" (
    "id" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "number" INTEGER NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Market_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketGeneralInfo" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "marketName" TEXT NOT NULL,
    "marketCategory" "MarketCategory" NOT NULL,
    "openingDays" "MarketOpeningDays" NOT NULL,

    CONSTRAINT "MarketGeneralInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketWaterSupply" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "connectedToPipeline" BOOLEAN NOT NULL,
    "waterAvailability" "WaterAvailability" NOT NULL,
    "availableDays" "WaterAvailabilityFrequency",
    "storageCapacity" "CleanWaterStorageCapacity" NOT NULL,
    "mainWaterSource" "MainWaterSource",
    "distanceToSource" "WaterSourceDistance" NOT NULL,

    CONSTRAINT "MarketWaterSupply_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketSanitation" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "toiletType" "ToiletFacilityType" NOT NULL,
    "slabConstructionMaterial" "FacilitySlabConstructionMaterial" NOT NULL,
    "totalToilets" INTEGER NOT NULL,
    "genderSeparation" BOOLEAN NOT NULL,
    "femaleToilets" INTEGER NOT NULL,
    "maleToilets" INTEGER NOT NULL,
    "girlsRoom" BOOLEAN NOT NULL,
    "disabilityAccess" BOOLEAN NOT NULL,
    "staffToilets" BOOLEAN NOT NULL,
    "hasToiletFullInLast2Years" BOOLEAN NOT NULL,
    "excretaManagement" "ExcretaManagement",

    CONSTRAINT "MarketSanitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketHygiene" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "handwashingFacility" BOOLEAN NOT NULL,
    "facilityType" "HandWashingFacilityType",
    "handwashingMaterials" "MarketHandWashingMaterial",
    "handWashingfacilityNearToilet" BOOLEAN,
    "toiletHandWashingFacilityType" "HandWashingFacilityType",
    "toiletHandWashingMaterials" "HandWashingMaterial",

    CONSTRAINT "MarketHygiene_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketSolidWasteManagement" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "wasteSeparation" BOOLEAN NOT NULL,
    "wasteManagement" "WasteManagementAfterSeparation",
    "treatmentType" "WasteTreatmentType",
    "collectionFrequency" "WasteCollectionFrequency",
    "collectionCost" INTEGER,

    CONSTRAINT "MarketSolidWasteManagement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketLiquidWasteManagement" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "liquidWasteManagement" "WasteWaterManagement" NOT NULL,

    CONSTRAINT "MarketLiquidWasteManagement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Location" (
    "id" TEXT NOT NULL,
    "villageId" INTEGER NOT NULL,
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "settlementType" "SettlementType" NOT NULL DEFAULT 'RURAL',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Location_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Submission" (
    "id" TEXT NOT NULL,
    "facilityType" "FacilityType" NOT NULL,
    "submittedById" TEXT NOT NULL,
    "submittedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "houseHoldId" TEXT,
    "schoolId" TEXT,
    "healthFacilityId" TEXT,
    "marketId" TEXT,

    CONSTRAINT "Submission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "School" (
    "id" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "number" INTEGER NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "School_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SchoolGeneralInfo" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "schoolName" TEXT NOT NULL,
    "schoolType" "SchoolType" NOT NULL,
    "managementType" "SchoolManagement" NOT NULL,
    "dayBoarding" "SchoolTypeDayBoarding" NOT NULL,
    "totalStudents" INTEGER NOT NULL,
    "femaleStudents" INTEGER NOT NULL,
    "maleStudents" INTEGER NOT NULL,
    "studentsWithDisabilities" INTEGER NOT NULL,

    CONSTRAINT "SchoolGeneralInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SchoolWaterSupply" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "connectedToPipeline" BOOLEAN NOT NULL,
    "waterAvailability" BOOLEAN NOT NULL,
    "availableDays" "WaterAvailabilityFrequency",
    "storageCapacity" "CleanWaterStorageCapacity" NOT NULL,
    "mainWaterSource" "MainWaterSource",
    "distanceToSource" "WaterSourceDistance" NOT NULL,

    CONSTRAINT "SchoolWaterSupply_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SchoolSanitation" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "toiletType" "ToiletFacilityType" NOT NULL,
    "slabConstructionMaterial" "FacilitySlabConstructionMaterial" NOT NULL,
    "totalToilets" INTEGER NOT NULL,
    "genderSeparation" BOOLEAN NOT NULL,
    "femaleToilets" INTEGER NOT NULL,
    "maleToilets" INTEGER NOT NULL,
    "girlsRoom" BOOLEAN NOT NULL,
    "disabilityAccess" BOOLEAN NOT NULL,
    "staffToilets" BOOLEAN NOT NULL,
    "hasToiletFullInLast2Years" BOOLEAN NOT NULL,
    "excretaManagement" "ExcretaManagement",

    CONSTRAINT "SchoolSanitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SchoolHygiene" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "handwashingFacility" BOOLEAN NOT NULL,
    "facilityType" "HandWashingFacilityType",
    "handwashingMaterials" "HandWashingMaterial",
    "handWashingfacilityNearToilet" BOOLEAN,
    "toiletHandWashingFacilityType" "HandWashingFacilityType",
    "toiletHandwashingMaterials" "HandWashingMaterial",

    CONSTRAINT "SchoolHygiene_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SchoolSolidWasteManagement" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "wasteSeparation" BOOLEAN NOT NULL,
    "wasteManagement" "WasteManagementAfterSeparation",
    "treatmentType" "WasteTreatmentType",
    "collectionFrequency" "WasteCollectionFrequency",
    "collectionCost" INTEGER,

    CONSTRAINT "SchoolSolidWasteManagement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SchoolLiquidWasteManagement" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "liquidWasteManagement" "WasteWaterManagement" NOT NULL,

    CONSTRAINT "SchoolLiquidWasteManagement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "telephoneNumber" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Account" (
    "userId" TEXT NOT NULL,
    "accountVerified" BOOLEAN NOT NULL DEFAULT false,
    "accountVerifiedAt" TIMESTAMP(3),
    "password" TEXT,
    "refreshToken" TEXT,
    "resetToken" TEXT,
    "resetTokenExpiry" TIMESTAMP(3),
    "is2FAEnabled" BOOLEAN NOT NULL DEFAULT false,
    "twoFASecret" TEXT,
    "otpTempToken" TEXT,
    "otpExpiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("userId")
);

-- CreateTable
CREATE TABLE "RecoveryCode" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "used" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RecoveryCode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "privileges" "Privilege"[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserLocationAccess" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "provinceId" INTEGER,
    "districtId" INTEGER,
    "sectorId" INTEGER,
    "cellId" INTEGER,
    "villageId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserLocationAccess_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WasteCollectionCompany" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "ownerName" TEXT NOT NULL,
    "ownerGender" "Gender" NOT NULL,
    "contactPhone" TEXT NOT NULL,
    "contactEmail" TEXT NOT NULL,
    "companyType" "ServiceProviderType" NOT NULL,
    "totalPersonnel" INTEGER NOT NULL,
    "femalePersonnel" INTEGER NOT NULL,
    "malePersonnel" INTEGER NOT NULL,
    "clientTypes" "ClientType"[],
    "wasteSeparation" BOOLEAN NOT NULL,
    "separatedMaterials" "WasteMaterial"[],
    "wasteDestination" "WasteDestination" NOT NULL,
    "destinationDetails" TEXT,
    "weighbridge" BOOLEAN NOT NULL,
    "recordingMethod" "RecordingMethod",

    CONSTRAINT "WasteCollectionCompany_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WasteRecoveryCompany" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "contactPerson" TEXT NOT NULL,
    "contactPhone" TEXT NOT NULL,
    "contactEmail" TEXT NOT NULL,
    "companyType" "ServiceProviderType" NOT NULL,
    "totalPersonnel" INTEGER NOT NULL,
    "femalePersonnel" INTEGER NOT NULL,
    "malePersonnel" INTEGER NOT NULL,
    "operationType" "OperationType" NOT NULL,
    "handledMaterials" TEXT[],
    "businessSites" TEXT[],

    CONSTRAINT "WasteRecoveryCompany_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WasteDisposalCompany" (
    "id" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "facilityLocation" TEXT NOT NULL,
    "contactPerson" TEXT NOT NULL,
    "contactPhone" TEXT NOT NULL,
    "contactEmail" TEXT NOT NULL,
    "companyType" "ServiceProviderType" NOT NULL,
    "totalPersonnel" INTEGER NOT NULL,
    "femalePersonnel" INTEGER NOT NULL,
    "malePersonnel" INTEGER NOT NULL,
    "clientTypes" "ClientType"[],
    "boundaryControl" BOOLEAN NOT NULL,
    "wasteDepositControl" BOOLEAN NOT NULL,
    "compactionFrequency" "CompactionFrequency" NOT NULL,
    "wasteBurning" BOOLEAN NOT NULL,
    "weighbridge" BOOLEAN NOT NULL,
    "wasteAmount" TEXT NOT NULL,
    "truckFrequency" "TruckFrequency" NOT NULL,
    "recordingMethod" "RecordingMethod",

    CONSTRAINT "WasteDisposalCompany_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Province_code_key" ON "Province"("code");

-- CreateIndex
CREATE UNIQUE INDEX "District_code_key" ON "District"("code");

-- CreateIndex
CREATE UNIQUE INDEX "Sector_code_key" ON "Sector"("code");

-- CreateIndex
CREATE UNIQUE INDEX "Cell_code_key" ON "Cell"("code");

-- CreateIndex
CREATE UNIQUE INDEX "Village_code_key" ON "Village"("code");

-- CreateIndex
CREATE UNIQUE INDEX "HealthFacility_number_key" ON "HealthFacility"("number");

-- CreateIndex
CREATE UNIQUE INDEX "HouseHold_number_key" ON "HouseHold"("number");

-- CreateIndex
CREATE UNIQUE INDEX "Market_number_key" ON "Market"("number");

-- CreateIndex
CREATE UNIQUE INDEX "School_number_key" ON "School"("number");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_telephoneNumber_key" ON "User"("telephoneNumber");

-- CreateIndex
CREATE INDEX "RecoveryCode_accountId_idx" ON "RecoveryCode"("accountId");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "Role"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Role_code_key" ON "Role"("code");

-- CreateIndex
CREATE INDEX "UserLocationAccess_userId_idx" ON "UserLocationAccess"("userId");

-- CreateIndex
CREATE INDEX "UserLocationAccess_districtId_idx" ON "UserLocationAccess"("districtId");

-- CreateIndex
CREATE INDEX "UserLocationAccess_sectorId_idx" ON "UserLocationAccess"("sectorId");

-- CreateIndex
CREATE INDEX "UserLocationAccess_villageId_idx" ON "UserLocationAccess"("villageId");

-- AddForeignKey
ALTER TABLE "District" ADD CONSTRAINT "District_provinceId_fkey" FOREIGN KEY ("provinceId") REFERENCES "Province"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Sector" ADD CONSTRAINT "Sector_districtId_fkey" FOREIGN KEY ("districtId") REFERENCES "District"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Cell" ADD CONSTRAINT "Cell_sectorId_fkey" FOREIGN KEY ("sectorId") REFERENCES "Sector"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Village" ADD CONSTRAINT "Village_cellId_fkey" FOREIGN KEY ("cellId") REFERENCES "Cell"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HealthFacility" ADD CONSTRAINT "HealthFacility_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HealthFacilityGeneralInfo" ADD CONSTRAINT "HealthFacilityGeneralInfo_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HealthFacilityWaterSupply" ADD CONSTRAINT "HealthFacilityWaterSupply_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HealthFacilitySanitation" ADD CONSTRAINT "HealthFacilitySanitation_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HealthFacilityHygiene" ADD CONSTRAINT "HealthFacilityHygiene_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HealthFacilitySolidWasteManagement" ADD CONSTRAINT "HealthFacilitySolidWasteManagement_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HealthFacilityLiquidWasteManagement" ADD CONSTRAINT "HealthFacilityLiquidWasteManagement_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HouseHold" ADD CONSTRAINT "HouseHold_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HouseHoldGeneralInfo" ADD CONSTRAINT "HouseHoldGeneralInfo_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HouseHoldWaterSupply" ADD CONSTRAINT "HouseHoldWaterSupply_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HouseHoldSanitation" ADD CONSTRAINT "HouseHoldSanitation_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HouseHoldHygiene" ADD CONSTRAINT "HouseHoldHygiene_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HouseHoldSolidWasteManagement" ADD CONSTRAINT "HouseHoldSolidWasteManagement_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HouseHoldLiquidWasteManagement" ADD CONSTRAINT "HouseHoldLiquidWasteManagement_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Market" ADD CONSTRAINT "Market_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketGeneralInfo" ADD CONSTRAINT "MarketGeneralInfo_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketWaterSupply" ADD CONSTRAINT "MarketWaterSupply_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketSanitation" ADD CONSTRAINT "MarketSanitation_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketHygiene" ADD CONSTRAINT "MarketHygiene_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketSolidWasteManagement" ADD CONSTRAINT "MarketSolidWasteManagement_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketLiquidWasteManagement" ADD CONSTRAINT "MarketLiquidWasteManagement_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Location" ADD CONSTRAINT "Location_villageId_fkey" FOREIGN KEY ("villageId") REFERENCES "Village"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Submission" ADD CONSTRAINT "Submission_submittedById_fkey" FOREIGN KEY ("submittedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Submission" ADD CONSTRAINT "Submission_houseHoldId_fkey" FOREIGN KEY ("houseHoldId") REFERENCES "HouseHold"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Submission" ADD CONSTRAINT "Submission_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "School"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Submission" ADD CONSTRAINT "Submission_healthFacilityId_fkey" FOREIGN KEY ("healthFacilityId") REFERENCES "HealthFacility"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Submission" ADD CONSTRAINT "Submission_marketId_fkey" FOREIGN KEY ("marketId") REFERENCES "Market"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "School" ADD CONSTRAINT "School_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SchoolGeneralInfo" ADD CONSTRAINT "SchoolGeneralInfo_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SchoolWaterSupply" ADD CONSTRAINT "SchoolWaterSupply_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SchoolSanitation" ADD CONSTRAINT "SchoolSanitation_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SchoolHygiene" ADD CONSTRAINT "SchoolHygiene_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SchoolSolidWasteManagement" ADD CONSTRAINT "SchoolSolidWasteManagement_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SchoolLiquidWasteManagement" ADD CONSTRAINT "SchoolLiquidWasteManagement_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RecoveryCode" ADD CONSTRAINT "RecoveryCode_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserLocationAccess" ADD CONSTRAINT "UserLocationAccess_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserLocationAccess" ADD CONSTRAINT "UserLocationAccess_provinceId_fkey" FOREIGN KEY ("provinceId") REFERENCES "Province"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserLocationAccess" ADD CONSTRAINT "UserLocationAccess_districtId_fkey" FOREIGN KEY ("districtId") REFERENCES "District"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserLocationAccess" ADD CONSTRAINT "UserLocationAccess_sectorId_fkey" FOREIGN KEY ("sectorId") REFERENCES "Sector"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserLocationAccess" ADD CONSTRAINT "UserLocationAccess_cellId_fkey" FOREIGN KEY ("cellId") REFERENCES "Cell"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserLocationAccess" ADD CONSTRAINT "UserLocationAccess_villageId_fkey" FOREIGN KEY ("villageId") REFERENCES "Village"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WasteCollectionCompany" ADD CONSTRAINT "WasteCollectionCompany_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WasteRecoveryCompany" ADD CONSTRAINT "WasteRecoveryCompany_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WasteDisposalCompany" ADD CONSTRAINT "WasteDisposalCompany_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "Submission"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
