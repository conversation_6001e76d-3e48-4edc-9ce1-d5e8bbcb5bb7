import { ApiProperty } from '@nestjs/swagger';

export class DistrictResponseDto {
  @ApiProperty({
    description: 'District ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'District code',
    example: 101,
  })
  code: number;

  @ApiProperty({
    description: 'District name',
    example: 'Nyarugenge',
  })
  name: string;

  @ApiProperty({
    description: 'Province information',
  })
  province: {
    id: number;
    name: string;
    code: number;
  };

  @ApiProperty({
    description: 'Number of sectors in this district',
    example: 10,
  })
  sectorCount: number;

  @ApiProperty({
    description: 'District creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'District last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class DistrictsListResponseDto {
  @ApiProperty({
    description: 'List of districts',
    type: [DistrictResponseDto],
  })
  districts: DistrictResponseDto[];

  @ApiProperty({
    description: 'Total number of districts',
    example: 30,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}
