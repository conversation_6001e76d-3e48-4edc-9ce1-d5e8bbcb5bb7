import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, IsEnum, IsBoolean, IsDate, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Gender, EducationLevel, MainWaterSource, WaterAvailability, WaterAvailabilityFrequency, CleanWaterStorageCapacity, WaterSourceDistance, WaterFetchingTime, UnimprovedWaterReason, PwsNonFunctionalityReason, PwsNonFunctionalityDuration, ToiletFacilityType, ToiletFacilityCompleteness, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';

export class HouseholdGeneralInfoDto {
  @ApiProperty()
  @IsString()
  headOfHouseholdName: string;

  @ApiProperty({ enum: Gender })
  @IsEnum(Gender)
  genderOfHead: Gender;

  @ApiProperty({ type: Date })
  @IsDate()
  @Transform(({ value }) => new Date(value))
  dateOfBirthOfHead: Date;

  @ApiProperty({ enum: EducationLevel })
  @IsEnum(EducationLevel)
  educationLevelOfHead: EducationLevel;

  @ApiProperty()
  @IsInt()
  householdSize: number;

  @ApiProperty()
  @IsInt()
  childrenUnder18: number;

  @ApiProperty()
  @IsInt()
  personsWithDisabilities: number;
}

export class HouseholdWaterSupplyDto {
  @ApiProperty({ enum: MainWaterSource })
  @IsEnum(MainWaterSource)
  waterSource: MainWaterSource;

  @ApiProperty({ enum: WaterAvailability })
  @IsEnum(WaterAvailability)
  @IsOptional()
  waterAvailability?: WaterAvailability;

  @ApiProperty({ enum: WaterAvailabilityFrequency, required: false })
  @IsOptional()
  @IsEnum(WaterAvailabilityFrequency)
  availableDays?: WaterAvailabilityFrequency;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  averageWaterCost?: number;

  @ApiProperty({ enum: CleanWaterStorageCapacity })
  @IsEnum(CleanWaterStorageCapacity)
  storageCapacity: CleanWaterStorageCapacity;

  @ApiProperty({ enum: WaterSourceDistance })
  @IsEnum(WaterSourceDistance)
  @IsOptional()
  distanceToSource?: WaterSourceDistance;

  @ApiProperty({ enum: WaterFetchingTime, required: false })
  @IsOptional()
  @IsEnum(WaterFetchingTime)
  timeToFetch?: WaterFetchingTime;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  jerryCanPrice?: number;

  @ApiProperty({ enum: UnimprovedWaterReason, required: false })
  @IsOptional()
  @IsEnum(UnimprovedWaterReason)
  unimprovedReason?: UnimprovedWaterReason;

  @ApiProperty({ enum: PwsNonFunctionalityReason, required: false })
  @IsOptional()
  @IsEnum(PwsNonFunctionalityReason)
  pwsNonFunctionalityReason?: PwsNonFunctionalityReason;

  @ApiProperty({ enum: PwsNonFunctionalityDuration, required: false })
  @IsOptional()
  @IsEnum(PwsNonFunctionalityDuration)
  pwsNonFunctionalityDuration?: PwsNonFunctionalityDuration;
}

export class HouseholdSanitationDto {
  @ApiProperty({ enum: ToiletFacilityType })
  @IsEnum(ToiletFacilityType)
  toiletType: ToiletFacilityType;

  @ApiProperty({ enum: ToiletFacilityCompleteness })
  @IsEnum(ToiletFacilityCompleteness)
  @IsOptional()
  toiletCompleteness?: ToiletFacilityCompleteness;

  @ApiProperty({ enum: FacilitySlabConstructionMaterial })
  @IsEnum(FacilitySlabConstructionMaterial)
  @IsOptional()
  slabConstructionMaterial?: FacilitySlabConstructionMaterial;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  hasToiletFullInLast2Years?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  toiletShared?: boolean;

  @ApiProperty({ enum: ExcretaManagement, required: false })
  @IsOptional()
  @IsEnum(ExcretaManagement)
  excretaManagement?: ExcretaManagement;
}

export class HouseholdHygieneDto {
  @ApiProperty()
  @IsBoolean()
  handwashingFacility: boolean;

  @ApiProperty({ enum: HandWashingFacilityType })
  @IsEnum(HandWashingFacilityType)
  @IsOptional()
  handWashingFacilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: HandWashingMaterial })
  @IsEnum(HandWashingMaterial)
  @IsOptional()
  handwashingMaterials?: HandWashingMaterial;
}

export class HouseholdSolidWasteManagementDto {
  @ApiProperty()
  @IsBoolean()
  wasteSeparation: boolean;

  @ApiProperty({ enum: WasteManagementAfterSeparation })
  @IsEnum(WasteManagementAfterSeparation)
  @IsOptional()
  wasteManagement?: WasteManagementAfterSeparation;

  @ApiProperty({ enum: WasteTreatmentType })
  @IsEnum(WasteTreatmentType)
  @IsOptional()
  treatmentType?: WasteTreatmentType;

  @ApiProperty({ enum: WasteCollectionFrequency })
  @IsEnum(WasteCollectionFrequency)
  @IsOptional()
  collectionFrequency?: WasteCollectionFrequency;
}

export class HouseholdLiquidWasteManagementDto {
  @ApiProperty({ enum: WasteWaterManagement })
  @IsEnum(WasteWaterManagement)
  wasterWaterManagement: WasteWaterManagement;
}

export class CreateHouseholdSubmissionDto extends BaseCreateSubmissionDto {
  @ApiProperty({ type: HouseholdGeneralInfoDto })
  @ValidateNested()
  @Type(() => HouseholdGeneralInfoDto)
  generalInfo: HouseholdGeneralInfoDto;

  @ApiProperty({ type: HouseholdWaterSupplyDto })
  @ValidateNested()
  @Type(() => HouseholdWaterSupplyDto)
  waterSupply: HouseholdWaterSupplyDto;

  @ApiProperty({ type: HouseholdSanitationDto })
  @ValidateNested()
  @Type(() => HouseholdSanitationDto)
  sanitation: HouseholdSanitationDto;

  @ApiProperty({ type: HouseholdHygieneDto })
  @ValidateNested()
  @Type(() => HouseholdHygieneDto)
  hygiene: HouseholdHygieneDto;

  @ApiProperty({ type: HouseholdSolidWasteManagementDto })
  @ValidateNested()
  @Type(() => HouseholdSolidWasteManagementDto)
  solidWaste: HouseholdSolidWasteManagementDto;

  @ApiProperty({ type: HouseholdLiquidWasteManagementDto })
  @ValidateNested()
  @Type(() => HouseholdLiquidWasteManagementDto)
  liquidWaste: HouseholdLiquidWasteManagementDto;
}

export class HouseholdSubmissionResponseDto extends BaseSubmissionResponseDto {
  @ApiProperty({ type: HouseholdGeneralInfoDto })
  generalInfo: HouseholdGeneralInfoDto;

  @ApiProperty({ type: HouseholdWaterSupplyDto })
  waterSupply: HouseholdWaterSupplyDto;

  @ApiProperty({ type: HouseholdSanitationDto })
  sanitation: HouseholdSanitationDto;

  @ApiProperty({ type: HouseholdHygieneDto })
  hygiene: HouseholdHygieneDto;

  @ApiProperty({ type: HouseholdSolidWasteManagementDto })
  solidWaste: HouseholdSolidWasteManagementDto;

  @ApiProperty({ type: HouseholdLiquidWasteManagementDto })
  liquidWaste: HouseholdLiquidWasteManagementDto;
}
