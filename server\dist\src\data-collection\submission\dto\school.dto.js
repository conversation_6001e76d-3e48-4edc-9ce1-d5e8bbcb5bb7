"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchoolSubmissionResponseDto = exports.CreateSchoolSubmissionDto = exports.SchoolLiquidWasteManagementDto = exports.SchoolSolidWasteManagementDto = exports.SchoolHygieneDto = exports.SchoolSanitationDto = exports.SchoolWaterSupplyDto = exports.SchoolGeneralInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
const base_submission_dto_1 = require("./base-submission.dto");
class SchoolGeneralInfoDto {
    schoolName;
    schoolType;
    managementType;
    dayBoarding;
    totalStudents;
    femaleStudents;
    maleStudents;
    studentsWithDisabilities;
}
exports.SchoolGeneralInfoDto = SchoolGeneralInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SchoolGeneralInfoDto.prototype, "schoolName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.SchoolType }),
    (0, class_validator_1.IsEnum)(client_1.SchoolType),
    __metadata("design:type", String)
], SchoolGeneralInfoDto.prototype, "schoolType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.SchoolManagement }),
    (0, class_validator_1.IsEnum)(client_1.SchoolManagement),
    __metadata("design:type", String)
], SchoolGeneralInfoDto.prototype, "managementType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.SchoolTypeDayBoarding }),
    (0, class_validator_1.IsEnum)(client_1.SchoolTypeDayBoarding),
    __metadata("design:type", String)
], SchoolGeneralInfoDto.prototype, "dayBoarding", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], SchoolGeneralInfoDto.prototype, "totalStudents", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], SchoolGeneralInfoDto.prototype, "femaleStudents", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], SchoolGeneralInfoDto.prototype, "maleStudents", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], SchoolGeneralInfoDto.prototype, "studentsWithDisabilities", void 0);
class SchoolWaterSupplyDto {
    connectedToPipeline;
    waterAvailability;
    availableDays;
    storageCapacity;
    mainWaterSource;
    distanceToSource;
}
exports.SchoolWaterSupplyDto = SchoolWaterSupplyDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolWaterSupplyDto.prototype, "connectedToPipeline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolWaterSupplyDto.prototype, "waterAvailability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterAvailabilityFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WaterAvailabilityFrequency),
    __metadata("design:type", String)
], SchoolWaterSupplyDto.prototype, "availableDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.CleanWaterStorageCapacity }),
    (0, class_validator_1.IsEnum)(client_1.CleanWaterStorageCapacity),
    __metadata("design:type", String)
], SchoolWaterSupplyDto.prototype, "storageCapacity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MainWaterSource, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MainWaterSource),
    __metadata("design:type", String)
], SchoolWaterSupplyDto.prototype, "mainWaterSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WaterSourceDistance }),
    (0, class_validator_1.IsEnum)(client_1.WaterSourceDistance),
    __metadata("design:type", String)
], SchoolWaterSupplyDto.prototype, "distanceToSource", void 0);
class SchoolSanitationDto {
    toiletType;
    slabConstructionMaterial;
    totalToilets;
    genderSeparation;
    femaleToilets;
    maleToilets;
    girlsRoom;
    disabilityAccess;
    staffToilets;
    hasToiletFullInLast2Years;
    excretaManagement;
}
exports.SchoolSanitationDto = SchoolSanitationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ToiletFacilityType }),
    (0, class_validator_1.IsEnum)(client_1.ToiletFacilityType),
    __metadata("design:type", String)
], SchoolSanitationDto.prototype, "toiletType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FacilitySlabConstructionMaterial }),
    (0, class_validator_1.IsEnum)(client_1.FacilitySlabConstructionMaterial),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SchoolSanitationDto.prototype, "slabConstructionMaterial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], SchoolSanitationDto.prototype, "totalToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolSanitationDto.prototype, "genderSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], SchoolSanitationDto.prototype, "femaleToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], SchoolSanitationDto.prototype, "maleToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolSanitationDto.prototype, "girlsRoom", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolSanitationDto.prototype, "disabilityAccess", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolSanitationDto.prototype, "staffToilets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolSanitationDto.prototype, "hasToiletFullInLast2Years", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.ExcretaManagement, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ExcretaManagement),
    __metadata("design:type", String)
], SchoolSanitationDto.prototype, "excretaManagement", void 0);
class SchoolHygieneDto {
    handwashingFacility;
    facilityType;
    handwashingMaterials;
    handWashingfacilityNearToilet;
    toiletHandWashingFacilityType;
    toiletHandwashingMaterials;
}
exports.SchoolHygieneDto = SchoolHygieneDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolHygieneDto.prototype, "handwashingFacility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    __metadata("design:type", String)
], SchoolHygieneDto.prototype, "facilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingMaterial, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingMaterial),
    __metadata("design:type", String)
], SchoolHygieneDto.prototype, "handwashingMaterials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolHygieneDto.prototype, "handWashingfacilityNearToilet", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingFacilityType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingFacilityType),
    __metadata("design:type", String)
], SchoolHygieneDto.prototype, "toiletHandWashingFacilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.HandWashingMaterial, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.HandWashingMaterial),
    __metadata("design:type", String)
], SchoolHygieneDto.prototype, "toiletHandwashingMaterials", void 0);
class SchoolSolidWasteManagementDto {
    wasteSeparation;
    wasteManagement;
    treatmentType;
    collectionFrequency;
    collectionCost;
}
exports.SchoolSolidWasteManagementDto = SchoolSolidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SchoolSolidWasteManagementDto.prototype, "wasteSeparation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteManagementAfterSeparation, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteManagementAfterSeparation),
    __metadata("design:type", String)
], SchoolSolidWasteManagementDto.prototype, "wasteManagement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteTreatmentType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteTreatmentType),
    __metadata("design:type", String)
], SchoolSolidWasteManagementDto.prototype, "treatmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteCollectionFrequency, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.WasteCollectionFrequency),
    __metadata("design:type", String)
], SchoolSolidWasteManagementDto.prototype, "collectionFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], SchoolSolidWasteManagementDto.prototype, "collectionCost", void 0);
class SchoolLiquidWasteManagementDto {
    liquidWasteManagement;
}
exports.SchoolLiquidWasteManagementDto = SchoolLiquidWasteManagementDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.WasteWaterManagement }),
    (0, class_validator_1.IsEnum)(client_1.WasteWaterManagement),
    __metadata("design:type", String)
], SchoolLiquidWasteManagementDto.prototype, "liquidWasteManagement", void 0);
class CreateSchoolSubmissionDto extends base_submission_dto_1.BaseCreateSubmissionDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.CreateSchoolSubmissionDto = CreateSchoolSubmissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolGeneralInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SchoolGeneralInfoDto),
    __metadata("design:type", SchoolGeneralInfoDto)
], CreateSchoolSubmissionDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolWaterSupplyDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SchoolWaterSupplyDto),
    __metadata("design:type", SchoolWaterSupplyDto)
], CreateSchoolSubmissionDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolSanitationDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SchoolSanitationDto),
    __metadata("design:type", SchoolSanitationDto)
], CreateSchoolSubmissionDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolHygieneDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SchoolHygieneDto),
    __metadata("design:type", SchoolHygieneDto)
], CreateSchoolSubmissionDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolSolidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SchoolSolidWasteManagementDto),
    __metadata("design:type", SchoolSolidWasteManagementDto)
], CreateSchoolSubmissionDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolLiquidWasteManagementDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SchoolLiquidWasteManagementDto),
    __metadata("design:type", SchoolLiquidWasteManagementDto)
], CreateSchoolSubmissionDto.prototype, "liquidWaste", void 0);
class SchoolSubmissionResponseDto extends base_submission_dto_1.BaseSubmissionResponseDto {
    generalInfo;
    waterSupply;
    sanitation;
    hygiene;
    solidWaste;
    liquidWaste;
}
exports.SchoolSubmissionResponseDto = SchoolSubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolGeneralInfoDto }),
    __metadata("design:type", SchoolGeneralInfoDto)
], SchoolSubmissionResponseDto.prototype, "generalInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolWaterSupplyDto }),
    __metadata("design:type", SchoolWaterSupplyDto)
], SchoolSubmissionResponseDto.prototype, "waterSupply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolSanitationDto }),
    __metadata("design:type", SchoolSanitationDto)
], SchoolSubmissionResponseDto.prototype, "sanitation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolHygieneDto }),
    __metadata("design:type", SchoolHygieneDto)
], SchoolSubmissionResponseDto.prototype, "hygiene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolSolidWasteManagementDto }),
    __metadata("design:type", SchoolSolidWasteManagementDto)
], SchoolSubmissionResponseDto.prototype, "solidWaste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SchoolLiquidWasteManagementDto }),
    __metadata("design:type", SchoolLiquidWasteManagementDto)
], SchoolSubmissionResponseDto.prototype, "liquidWaste", void 0);
//# sourceMappingURL=school.dto.js.map