import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateCellDto } from './create-cell.dto';

export class UpdateCellDto extends PartialType(CreateCellDto) {}

export class UpdateCellResponseDto {
  @ApiProperty({
    description: 'Updated cell information',
  })
  cell: {
    id: number;
    code: number;
    name: string;
    sectorId: number;
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Cell updated successfully',
  })
  message: string;
}
