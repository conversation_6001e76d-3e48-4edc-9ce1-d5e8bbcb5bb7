import { BaseFacilityWithNameDto, BaseFacilityWithNameResponseDto, BasePaginatedResponseDto, BaseCreateResponseDto, BaseUpdateResponseDto } from './base-facility.dto';
import { PublicPlaceType } from '@prisma/client';
export declare class CreatePublicPlaceDto extends BaseFacilityWithNameDto {
    type: PublicPlaceType;
}
declare const UpdatePublicPlaceDto_base: import("@nestjs/common").Type<Partial<CreatePublicPlaceDto>>;
export declare class UpdatePublicPlaceDto extends UpdatePublicPlaceDto_base {
}
export declare class PublicPlaceResponseDto extends BaseFacilityWithNameResponseDto {
    type: PublicPlaceType;
}
export declare class PublicPlacesListResponseDto extends BasePaginatedResponseDto<PublicPlaceResponseDto> {
    data: PublicPlaceResponseDto[];
}
export declare class CreatePublicPlaceResponseDto extends BaseCreateResponseDto<PublicPlaceResponseDto> {
    facility: PublicPlaceResponseDto;
    message: string;
}
export declare class UpdatePublicPlaceResponseDto extends BaseUpdateResponseDto<PublicPlaceResponseDto> {
    facility: PublicPlaceResponseDto;
    message: string;
}
export {};
