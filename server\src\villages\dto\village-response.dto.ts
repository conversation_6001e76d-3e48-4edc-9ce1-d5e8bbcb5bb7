import { ApiProperty } from '@nestjs/swagger';

export class VillageResponseDto {
  @ApiProperty({
    description: 'Village ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Village code',
    example: 101010101,
  })
  code: number;

  @ApiProperty({
    description: 'Village name',
    example: 'Ubumwe',
  })
  name: string;

  @ApiProperty({
    description: 'Cell information',
  })
  cell: {
    id: number;
    name: string;
    code: number;
    sector: {
      id: number;
      name: string;
      code: number;
      district: {
        id: number;
        name: string;
        code: number;
        province: {
          id: number;
          name: string;
          code: number;
        };
      };
    };
  };

  @ApiProperty({
    description: 'Village creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Village last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class VillagesListResponseDto {
  @ApiProperty({
    description: 'List of villages',
    type: [VillageResponseDto],
  })
  villages: VillageResponseDto[];

  @ApiProperty({
    description: 'Total number of villages',
    example: 14837,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 1484,
  })
  totalPages: number;
}
