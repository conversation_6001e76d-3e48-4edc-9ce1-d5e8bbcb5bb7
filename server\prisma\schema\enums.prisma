enum SettlementType {
    RURAL
    URBAN
}

enum EducationLevel {
    NONE     @map("none")
    PRIMARY  @map("primary")
    O_LEVEL  @map("O level")
    A_LEVEL  @map("A level")
    TERTIARY @map("tertiary")
}

enum Gender {
    MALE
    FEMALE
    OTHER
}

enum AgeGroup {
    BELOW_18          @map("below 18")
    BETWEEN_18_AND_30 @map("between 18 and 30")
    BETWEEN_31_AND_50 @map("between 31 and 50")
    ABOVE_50          @map("above 50")
}

enum MainWaterSource {
    WATER_TAP_WITHIN_HH        @map("water tap within the HH/Facility")
    PUBLIC_WATER_TAP_KIOSK     @map("Public water tap/kiosk")
    PROTECTED_IMPROVED_SPRINGS @map("Protected/improved springs")
    BOREHOLE                   @map("Borehole")
    RAIN_WATER                 @map("Rain water")
    UNIMPROVED_SPRINGS         @map("Unimproved springs")
    SURFACE_WATER              @map("Surface water (Lake, rivers, etc.)")
    FROM_NEIGHBOR              @map("From neighbor")
}

enum WaterAvailability {
    ALWAYS_AVAILABLE      @map("HH members can find water whenever needed")
    SOMETIMES_UNAVAILABLE @map("Sometimes HH members cannot find water to use")
}

enum WaterAvailabilityFrequency {
    ONCE_PER_WEEK       @map("Once per week")
    TWICE_A_WEEK        @map("Twice a week")
    THREE_DAYS_A_WEEK   @map("3 days a week")
    ONCE_PER_MONTH      @map("Once a month")
    MORE_THAN_ONE_MONTH @map("Once in a period more than a month")
    NONE                @map("none of the above")
}

enum CleanWaterStorageCapacity {
    LITERS_1_TO_500   @map("1 - 500 liters")
    LITERS_501_TO_2M3 @map("501 liters - 2 m3")
    M3_2_TO_5         @map("2m3 - 5m3")
    M3_5_TO_10        @map("5m3 - 10m3")
    ABOVE_10M3        @map("Above 10m3")
}

enum WaterSourceDistance {
    METERS_1_TO_200   @map("1 m - 200 m")
    METERS_201_TO_500 @map("201 m- 500 m")
    METERS_501_TO_1KM @map("501 m - 1 km")
    ABOVE_1KM         @map("Above 1 km")
}

enum WaterFetchingTime {
    MINUTES_1_TO_30  @map("1 min - 30 min")
    MINUTES_31_TO_60 @map("31 min - 1hour min")
    ABOVE_ONE_HOUR   @map("Above one hour")
}

enum UnimprovedWaterReason {
    IMPROVED_SOURCE_FAR           @map("improved water source is far")
    NEARBY_SOURCE_NOT_FUNCTIONING @map("The nearby public water source does not function")
    OTHER                         @map("Other")
}

enum PwsNonFunctionalityReason {
    NEVER_FUNCTIONED @map("PWS did never function at all")
    WATER_VANISHED   @map("Water vanished from the PWS")
    DAMAGED          @map("Damaged")
}

enum PwsNonFunctionalityDuration {
    ONE_WEEK_TO_ONE_MONTH @map("Between 1 week and a month")
    ONE_TO_THREE_MONTHS   @map("1 month - 3 months")
    ABOVE_THREE_MONTHS    @map("Above 3 months")
}

enum ToiletFacilityType {
    FLUSH_TO_SEWER_SYSTEM      @map("Flush to piped sewer system")
    FLUSH_TO_SEPTIC_TANK       @map("Flush to septic tank")
    FLUSH_TO_PIT               @map("Flush to pit")
    VENTILATED_IMPROVED_PIT    @map("Ventilated improved pit latrine")
    PIT_WITH_SLAB              @map("Pit latrine with slab")
    PIT_WITHOUT_SLAB           @map("Pit latrine without slab")
    COMPOSTING_TOILET          @map("Composting toilet (Ecosan)")
    URINE_DIVERSION_DRY_TOILET @map("Urine diversion dry toilet")
    NO_FACILITY                @map("No facility/bush/field")
    OTHER                      @map("Other (specify):")
}

enum ToiletFacilityCompleteness {
    ROOF_AND_CLOSING_DOOR @map("Has roof and closing door")
    ROOF_NO_CLOSING_DOOR  @map("Has roof but without closing door")
    CLOSING_DOOR_NO_ROOF  @map("Has closing door, but no roof")
    INCOMPLETE_WALLS      @map("Has not complete walls")
    NO_WALLS              @map("Has no walls")
}

enum FacilitySlabConstructionMaterial {
    DRY_TREES_OR_UNORDERED_WOOD @map("Dry trees or unordered wood ")
    WOOD_WELL_ORDERED           @map("Wood well ordered (without holes other than the drop hole)")
    SOLID_LAND_WITH_HOLES       @map("Solid land with holes (besides the drop hole)")
    SOLID_LAND_NO_HOLES         @map("Solid land floor without holes")
    CONCRETE_WITH_CEMENT_FLOOR  @map("Concrete with cement floor")
    CONCRETE_WITH_TILES_FLOOR   @map("Concrete with tiles floor")
}

enum ExcretaManagement {
    EMPTIED_BY_SPECIALIZED_COMPANY @map("Excreta was emptied and transported by a specialized company")
    FILLED_WITH_LAND_AND_LEFT      @map("The toilet was filled with land, ashes, etc. and was left")
    LEFT_WITHOUT_FILLING           @map("The toilet was left without filling it with land, ashes, etc.")
    FILLED_AND_COMPOSTED           @map("The toilet was filled with land, ashes, etc. then composted")
    DIRECTED_TO_ANOTHER_PIT        @map("The toilet was directed (discharged) into another pit")
}

enum HandWashingFacilityType {
    MOBILE @map("Mobile facility")
    FIXED  @map("Fixed facility")
}

enum HandWashingMaterial {
    WATER_AND_SOAP @map("Water and soap")
    WATER_SOAP_SANITIZER @map("Water, soap and sanitizer")
    SANITIZER   @map("Sanitizer")
    ONLY_WATER     @map("Only water")
    NONE           @map("None of them")
}

enum PublicPlaceHandWashingMaterial {
    WATER_SOAP_SANITIZER   @map("Water, soap and sanitizer")
    WATER_AND_SOAP         @map("Water and soap")
    SANITIZER_ONLY         @map("Sanitizer")
    ONLY_WATER             @map("Only water")
    NEITHER_WATER_NOR_SOAP @map("Neither water nor soap")
}

enum WasteWaterManagement {
    DEDICATED_PIT_COVERED     @map("Channeled to a dedicated pit which is covered")
    DEDICATED_PIT_NOT_COVERED @map("Channeled to a dedicated pit which is not covered")
    SEPTIC_TANK               @map("Channeled to septic tank")
    SEWERS                    @map("Channeled to sewers")
    OPEN_TRENCHES_OR_DRAINS   @map("Flows in open trenches/drains")
    FLOWS_ON_GROUND           @map("Flows on ground")
    RECYCLED                  @map("They get recycled")
}

enum WasteManagementAfterSeparation {
    TRANSPORTED_BY_COMPANIES @map("Transported by waste collection companies")
    ON_SITE_TREATMENT        @map("on site treatment")
    BOTH                     @map("Both (a) and (b)")
}

enum WasteTreatmentType {
    COMPOSTING            @map("Composting (fertilizer)")
    BURIED_UNDER_GROUND   @map("Buried under ground.")
    BURNT                 @map("Burnt.")
    SORT_AND_SELL         @map("Sort materials and sell to specialized companies")
    AUTOCLAVING           @map("Autoclaving")
    INCINERATION          @map("Incineration")
    MICROWAVE_TREATMENT   @map("Microwave treatment")
    CHEMICAL_DISINFECTION @map("Chemical disinfection")
}

enum WasteCollectionFrequency {
    DAILY            @map("Daily")
    TWO_TIMES_WEEK   @map("2 times a week")
    THREE_TIMES_WEEK @map("3 times a week")
    WEEKLY           @map("Weekly")
    OTHER            @map("Other: (please specify)")
}

enum SchoolType {
    ECD                          @map("Early Childhood Development (ECD)")
    PRE_PRIMARY                  @map("Pre-primary")
    PRIMARY                      @map("Primary")
    SECONDARY                    @map("Secondary")
    COMBINED_PRE_PRIMARY_PRIMARY @map("Combined pre-primary & primary")
    COMBINED_PRIMARY_SECONDARY   @map("Combined primary & secondary")
    COMBINED_ALL_LEVELS          @map("Combined pre-primary, primary & secondary")
    HIGHER_EDUCATION             @map("higher education")
}

enum SchoolManagement {
    PUBLIC                  @map("Public School")
    PRIVATE                 @map("Private school")
    FAITH_BASED             @map("Faith-based")
    FAITH_BASED_CO_FINANCED @map("Faith-based co-financed by Government")
    PRIVATE_CO_FINANCED     @map("Private co-financed by Government")
}

enum SchoolTypeDayBoarding {
    DAY_SCHOOL       @map("Day school")
    BOARDING_SCHOOL  @map("Boarding school")
    DAY_AND_BOARDING @map("Day and boarding school")
}

enum HealthFacilityType {
    REFERRAL_HOSPITAL   @map("Referral hospital")
    PROVINCIAL_HOSPITAL @map("Provincial hospital")
    DISTRICT_HOSPITAL   @map("District hospital")
    HEALTH_CENTER       @map("Health Center")
    HEALTH_POST         @map("health post")
    POLYCLINIC          @map("Polyclinic")
    CLINIC              @map("Clinic")
    DISPENSARY          @map("Dispensary")
    OTHERS              @map("Others")
}

enum HealthFacilityManagement {
    PUBLIC                        @map("Public")
    PRIVATE                       @map("Private")
    FAITH_BASED                   @map("Faith-based")
    PRIVATE_SUPPORTED_BY_GOVT     @map("Private HF supported by Govt")
    FAITH_BASED_SUPPORTED_BY_GOVT @map("Faith-based HF supported by Govt")
}

enum DailyPatientVolume {
    ONE_TO_FIFTY                 @map("1-50")
    FIFTY_ONE_TO_ONE_HUNDRED     @map("51-100")
    ONE_HUNDRED_TO_THREE_HUNDRED @map("100-300")
    ABOVE_THREE_HUNDRED          @map("above 300")
}

enum PublicPlaceCategory {
    FULLY_CONSTRUCTED     @map("fully constructed")
    PARTIALLY_CONSTRUCTED @map("partially constructed")
    NOT_CONSTRUCTED       @map("Not constructed")
}

enum PublicPlaceOpeningDays {
    ONCE_A_WEEK       @map("Once a week")
    TWICE_A_WEEK      @map("Twice a week")
    THREE_DAYS_A_WEEK @map("three days a week")
    EVERY_DAY         @map("Every day")
}

enum ServiceProviderType {
    MUNICIPAL_PUBLIC_OPERATOR  @map("municipal/public operator")
    PRIVATE_SERVICE_CONTRACTOR @map("Private service contractor")
    NGO_CSOS                   @map("NGO/CSOs")
    OTHER                      @map("Other")
}

enum ClientType {
    HOUSEHOLDS                            @map("Households")
    MARKETS                               @map("Markets")
    SCHOOLS                               @map("Schools")
    HEALTH_FACILITIES                     @map("Health facilities")
    INDUSTRIES_FACTORIES                  @map("Industries/Factories")
    BUSINESSES_RESTAURANTS_HOTELS_VENDORS @map("Businesses/restaurants/ Hotels/(street) vendors")
    ADMINISTRATIVE_BUILDINGS              @map("Administrative buildings")
    OTHERS                                @map("Others")
}

enum WasteMaterial {
    BIODEGRADABLE     @map("Biodegradable")
    NON_BIODEGRADABLE @map("Non-biodegradable")
}

enum WasteDestination {
    DUMPSITE_LANDFILL          @map("Dumpsite/landfill")
    MATERIAL_RECOVERY_FACILITY @map("Material recovery/recycling facility")
    OTHERS                     @map("Others")
}

enum RecordingMethod {
    PAPER_LOGBOOK @map("paper logbook")
    OTHER         @map("others")
}

enum OperationType {
    END_OF_CHAIN_RECYCLER @map("End-of-chain recycler/recoverer facility ")
    APEX_TRADER           @map("Apex trader (Buying and Selling recycled commodities)")
    INTERMEDIATE_TRADER   @map("Intermediate trader person or company")
}

enum CompactionFrequency {
    DAILY          @map("Daily")
    WEEKLY         @map("Weekly")
    MONTHLY        @map("Monthly")
    ANNUALLY       @map("Annually")
    EMERGENCY_ONLY @map("Emergency only")
    NEVER          @map("Never")
}

enum TruckFrequency {
    DAILY  @map("Daily")
    WEEKLY @map("Weekly")
    OTHER  @map("Other")
}

enum PublicPlaceType {
    MARKET
    PUBLIC_TRANSPORT_STOP
}