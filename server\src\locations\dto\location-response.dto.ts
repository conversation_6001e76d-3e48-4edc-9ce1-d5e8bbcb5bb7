import { ApiProperty } from '@nestjs/swagger';
import { SettlementType } from '@prisma/client';

export class LocationResponseDto {
  @ApiProperty({
    description: 'Location ID',
    example: 'location_123',
  })
  id: string;

  @ApiProperty({
    description: 'Village ID',
    example: 1,
  })
  villageId: number;

  @ApiProperty({
    description: 'Village information',
  })
  village: {
    id: number;
    code: number;
    name: string;
    cell: {
      id: number;
      code: number;
      name: string;
      sector: {
        id: number;
        code: number;
        name: string;
        district: {
          id: number;
          code: number;
          name: string;
          province: {
            id: number;
            code: number;
            name: string;
          };
        };
      };
    };
  };

  @ApiProperty({
    description: 'Latitude coordinate',
    example: -1.9441,
    required: false,
  })
  latitude?: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 30.0619,
    required: false,
  })
  longitude?: number;

  @ApiProperty({
    description: 'Settlement type',
    enum: SettlementType,
    example: SettlementType.URBAN,
  })
  settlementType: SettlementType;

  @ApiProperty({
    description: 'Location creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Location last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class LocationsListResponseDto {
  @ApiProperty({
    description: 'List of locations',
    type: [LocationResponseDto],
  })
  locations: LocationResponseDto[];

  @ApiProperty({
    description: 'Total number of locations',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages: number;
}

export class DeleteLocationResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Location deleted successfully',
  })
  message: string;
}
