import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateLocationDto, CreateLocationResponseDto } from './dto/create-location.dto';
import { UpdateLocationDto, UpdateLocationResponseDto } from './dto/update-location.dto';
import {
  LocationResponseDto,
  LocationsListResponseDto,
  DeleteLocationResponseDto,
} from './dto/location-response.dto';

@Injectable()
export class LocationsService {
  constructor(private prisma: PrismaService) {}

  async create(createLocationDto: CreateLocationDto): Promise<CreateLocationResponseDto> {
    // Verify village exists
    const village = await this.prisma.village.findUnique({
      where: { id: createLocationDto.villageId },
      include: {
        cell: {
          include: {
            sector: {
              include: {
                district: {
                  include: {
                    province: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!village) {
      throw new NotFoundException('Village not found');
    }

    const location = await this.prisma.location.create({
      data: {
        villageId: createLocationDto.villageId,
        latitude: createLocationDto.latitude,
        longitude: createLocationDto.longitude,
        settlementType: createLocationDto.settlementType,
      },
      include: {
        village: {
          include: {
            cell: {
              include: {
                sector: {
                  include: {
                    district: {
                      include: {
                        province: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      location: this.formatLocationResponse(location),
      message: 'Location created successfully',
    };
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
  ): Promise<LocationsListResponseDto> {
    const skip = (page - 1) * limit;

    const where = search
      ? {
          village: {
            name: { contains: search, mode: 'insensitive' as const },
          },
        }
      : {};

    const [locations, total] = await Promise.all([
      this.prisma.location.findMany({
        where,
        skip,
        take: limit,
        include: {
          village: {
            include: {
              cell: {
                include: {
                  sector: {
                    include: {
                      district: {
                        include: {
                          province: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.location.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      locations: locations.map((location) => this.formatLocationResponse(location)),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: string): Promise<LocationResponseDto> {
    const location = await this.prisma.location.findUnique({
      where: { id },
      include: {
        village: {
          include: {
            cell: {
              include: {
                sector: {
                  include: {
                    district: {
                      include: {
                        province: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!location) {
      throw new NotFoundException('Location not found');
    }

    return this.formatLocationResponse(location);
  }

  async update(
    id: string,
    updateLocationDto: UpdateLocationDto,
  ): Promise<UpdateLocationResponseDto> {
    const existingLocation = await this.prisma.location.findUnique({
      where: { id },
    });

    if (!existingLocation) {
      throw new NotFoundException('Location not found');
    }

    // Verify village exists if provided
    if (updateLocationDto.villageId) {
      const village = await this.prisma.village.findUnique({
        where: { id: updateLocationDto.villageId },
      });

      if (!village) {
        throw new NotFoundException('Village not found');
      }
    }

    const location = await this.prisma.location.update({
      where: { id },
      data: updateLocationDto,
      include: {
        village: {
          include: {
            cell: {
              include: {
                sector: {
                  include: {
                    district: {
                      include: {
                        province: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      location: this.formatLocationResponse(location),
      message: 'Location updated successfully',
    };
  }

  async remove(id: string): Promise<DeleteLocationResponseDto> {
    const location = await this.prisma.location.findUnique({
      where: { id },
      include: {
        houseHolds: true,
        schools: true,
        healthFacilities: true,
      },
    });

    if (!location) {
      throw new NotFoundException('Location not found');
    }

    // Check if location is being used by any facilities
    const facilitiesCount = 
      location.houseHolds.length +
      location.schools.length +
      location.healthFacilities.length;

    if (facilitiesCount > 0) {
      throw new ConflictException(
        `Location is in use by ${facilitiesCount} facility(ies) and cannot be deleted`,
      );
    }

    await this.prisma.location.delete({
      where: { id },
    });

    return {
      message: 'Location deleted successfully',
    };
  }

  private formatLocationResponse(location: any): LocationResponseDto {
    return {
      id: location.id,
      villageId: location.villageId,
      village: {
        id: location.village.id,
        name: location.village.name,
        code: location.village.code,
        cell: {
          id: location.village.cell.id,
          name: location.village.cell.name,
          code: location.village.cell.code,
          sector: {
            id: location.village.cell.sector.id,
            name: location.village.cell.sector.name,
            code: location.village.cell.sector.code,
            district: {
              id: location.village.cell.sector.district.id,
              name: location.village.cell.sector.district.name,
              code: location.village.cell.sector.district.code,
              province: {
                id: location.village.cell.sector.district.province.id,
                name: location.village.cell.sector.district.province.name,
                code: location.village.cell.sector.district.province.code,
              },
            },
          },
        },
      },
      latitude: location.latitude,
      longitude: location.longitude,
      settlementType: location.settlementType,
      createdAt: location.createdAt,
      updatedAt: location.updatedAt,
    };
  }
}
