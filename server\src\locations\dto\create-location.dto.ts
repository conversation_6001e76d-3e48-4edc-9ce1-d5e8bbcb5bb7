import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsNumber, Min, IsEnum } from 'class-validator';
import { SettlementType } from '@prisma/client';

export class CreateLocationDto {
  @ApiProperty({
    description: 'Village ID where the location is situated',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  villageId: number;

  @ApiProperty({
    description: 'Latitude coordinate',
    example: -1.9441,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 30.0619,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiProperty({
    description: 'Settlement type',
    enum: SettlementType,
    example: SettlementType.URBAN,
    required: false,
    default: SettlementType.RURAL,
  })
  @IsOptional()
  @IsEnum(SettlementType)
  settlementType?: SettlementType = SettlementType.RURAL;
}

export class CreateLocationResponseDto {
  @ApiProperty({
    description: 'Created location information',
  })
  location: {
    id: string;
    villageId: number;
    village: {
      id: number;
      name: string;
      cell: {
        id: number;
        name: string;
        sector: {
          id: number;
          name: string;
          district: {
            id: number;
            name: string;
            province: {
              id: number;
              name: string;
            };
          };
        };
      };
    };
    latitude?: number;
    longitude?: number;
    settlementType: SettlementType;
    createdAt: Date;
    updatedAt: Date;
  };

  @ApiProperty({
    description: 'Success message',
    example: 'Location created successfully',
  })
  message: string;
}
