services:
  postgres:
    image: postgres:15
    container_name: wash-mis-postgres
    restart: always
    ports:
      - '5438:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: wash_mis
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: .
    container_name: wash-mis-server
    restart: always
    ports:
      - '8085:8080'
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - .env.docker
    command: sh -c "pnpm prisma migrate deploy --schema ./prisma/schema && pnpm seed && node dist/src/main"

volumes:
  postgres_data:
